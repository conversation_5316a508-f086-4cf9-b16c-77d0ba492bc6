-- ./excel/achieve/achievetask.xlsx
return {

    [31001] = {
        achievetype = 1,
        condition = "접속보상 수령=1",
        degreetype = 1,
        describe = "접속보상 1회 수령",
        id = 31001,
        name = "접속보상",
        open_id = 1042,
        pre_condition = {"캐릭터 레벨=6"},
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=5000)"}},
        tasktype = 1,
        type = 3,
    },

    [31002] = {
        achievetype = 1,
        condition = "친구수=1",
        degreetype = 1,
        describe = "친구 1명 보유",
        id = 31002,
        name = "동반동유",
        open_id = 1041,
        pre_condition = {"업적퀘스트 완료=31001", "캐릭터레벨=10"},
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=8000)"}},
        tasktype = 1,
        type = 3,
    },

    [31003] = {
        achievetype = 1,
        condition = "캐릭터스킬2레벨횟수=1",
        degreetype = 2,
        describe = "캐릭터 임의 스킬 2레벨로 강화",
        id = 31003,
        name = "스킬강화",
        open_id = 102,
        pre_condition = {"업적퀘스트 완료=31002"},
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=10000)"}},
        tasktype = 1,
        type = 3,
    },

    [31010] = {
        achievetype = 1,
        condition = "길드1레벨이상=1",
        degreetype = 1,
        describe = "길드 1개 가입",
        id = 31010,
        name = "길드가입",
        open_id = 600,
        pre_condition = {"업적퀘스트 완료=31003", "캐릭터레벨=14"},
        rewarditem = {{["num"] = 1, ["sid"] = "1014(value=200)"}},
        tasktype = 1,
        type = 3,
    },

    [31011] = {
        achievetype = 1,
        condition = "길드건설횟수=1",
        degreetype = 1,
        describe = "길드 1회 건설",
        id = 31011,
        name = "길드건설",
        open_id = 601,
        pre_condition = {"업적퀘스트 완료=31010"},
        rewarditem = {{["num"] = 1, ["sid"] = "1014(value=300)"}},
        tasktype = 1,
        type = 3,
    },

    [31012] = {
        achievetype = 1,
        condition = "파트너조각소원=1",
        degreetype = 1,
        describe = "파트너 조각 소원 1회 진행",
        id = 31012,
        name = "소원",
        open_id = 1044,
        pre_condition = {"업적퀘스트 완료=31011"},
        rewarditem = {{["num"] = 1, ["sid"] = "1014(value=100)"}},
        tasktype = 1,
        type = 3,
    },

    [31013] = {
        achievetype = 1,
        condition = "길드현상금도전=1",
        degreetype = 1,
        describe = "길드 상금 1회 도전",
        id = 31013,
        name = "상금",
        open_id = 603,
        pre_condition = {"업적퀘스트 완료=31012"},
        rewarditem = {{["num"] = 1, ["sid"] = "1014(value=500)"}},
        tasktype = 1,
        type = 3,
    },

    [31018] = {
        achievetype = 1,
        condition = "사제퀘스트=3",
        degreetype = 2,
        describe = "순찰잡무 3회 완료",
        id = 31018,
        name = "순찰잡무",
        open_id = 1039,
        pre_condition = {"업적퀘스트 완료=31013", "캐릭터레벨=15"},
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=20000)"}},
        tasktype = 1,
        type = 3,
    },

    [31015] = {
        achievetype = 1,
        condition = "캐릭터스킬3레벨횟수=2",
        degreetype = 2,
        describe = "캐릭터 임의 스킬 3레벨로 강화",
        id = 31015,
        name = "스킬강화",
        open_id = 102,
        pre_condition = {},
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=15000)"}},
        tasktype = 1,
        type = 3,
    },

    [31026] = {
        achievetype = 1,
        condition = "친구저택방문=1",
        degreetype = 1,
        describe = "친구저택 방문1회",
        id = 31026,
        name = "저택방문",
        open_id = 1052,
        pre_condition = {"업적퀘스트 완료=31018", "캐릭터레벨=20"},
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=15000)"}},
        tasktype = 1,
        type = 3,
    },

    [31022] = {
        achievetype = 1,
        condition = "일일수행횟수=10",
        degreetype = 2,
        describe = "일일수행 10회 참여",
        id = 31022,
        name = "일일수행",
        open_id = 1053,
        pre_condition = {"업적퀘스트 완료=31026", "캐릭터레벨=25"},
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=20000)"}},
        tasktype = 1,
        type = 3,
    },

    [31030] = {
        achievetype = 1,
        condition = "영혼상자 오픈=1",
        degreetype = 1,
        describe = "영혼상자 오픈1회",
        id = 31030,
        name = "영혼상자",
        open_id = 1057,
        pre_condition = {"업적퀘스트 완료=31022", "캐릭터레벨=28"},
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=20)"}},
        tasktype = 1,
        type = 3,
    },

    [31027] = {
        achievetype = 1,
        condition = "매골지-현관 클리어=1",
        degreetype = 1,
        describe = "장비 던전-현관 1회 클리어",
        id = 31027,
        name = "장비던전",
        open_id = 1049,
        pre_condition = {"업적퀘스트 완료=31030", "캐릭터레벨=30"},
        rewarditem = {{["num"] = 4, ["sid"] = "11001"}},
        tasktype = 1,
        type = 3,
    },

    [31004] = {
        achievetype = 1,
        condition = "매골지－비밀통로 클리어=1",
        degreetype = 1,
        describe = "장비 던전-비밀통로 1회 클리어",
        id = 31004,
        name = "장비던전",
        open_id = 1050,
        pre_condition = {"업적퀘스트 완료=31027"},
        rewarditem = {{["num"] = 4, ["sid"] = "11001"}},
        tasktype = 1,
        type = 3,
    },

    [31005] = {
        achievetype = 1,
        condition = "매골지－비밀감옥 클리어=1",
        degreetype = 1,
        describe = "장비 던전-비밀감옥 1회 클리어",
        id = 31005,
        name = "장비던전",
        open_id = 1051,
        pre_condition = {"업적퀘스트 완료=31004"},
        rewarditem = {{["num"] = 4, ["sid"] = "11001"}},
        tasktype = 1,
        type = 3,
    },

    [31006] = {
        achievetype = 1,
        condition = "30레벨 모든 장비 장착=1",
        degreetype = 1,
        describe = "캐릭터 6개 30레벨 장비 장착 완료",
        id = 31006,
        name = "장비장착",
        open_id = 101,
        pre_condition = {"업적 퀘스트 완료=31005"},
        rewarditem = {{["num"] = 8, ["sid"] = "11001"}},
        tasktype = 1,
        type = 3,
    },

    [31008] = {
        achievetype = 1,
        condition = "장비 3레벨 돌파=6",
        degreetype = 2,
        describe = "전신 6개 장비 돌파+3",
        id = 31008,
        name = "돌파",
        open_id = 301,
        pre_condition = {"업적 퀘스트 완료=31006"},
        rewarditem = {{["num"] = 12, ["sid"] = "11001"}},
        tasktype = 1,
        type = 3,
    },

    [31017] = {
        achievetype = 1,
        condition = "보석 3레벨 횟수=1",
        degreetype = 2,
        describe = "임의 위치의 보석 3레벨 달성",
        id = 31017,
        name = "보석강화",
        open_id = 303,
        pre_condition = {"업적 퀘스트 완료=31008"},
        rewarditem = {{["num"] = 1, ["sid"] = "18001"}},
        tasktype = 1,
        type = 3,
    },

    [31028] = {
        achievetype = 1,
        condition = "무관 도전=1",
        degreetype = 1,
        describe = "무관 도전 1회",
        id = 31028,
        name = "무관도전",
        open_id = 1056,
        pre_condition = {"업적 퀘스트 완료=31017", "캐릭터 레벨=35"},
        rewarditem = {{["num"] = 1, ["sid"] = "1009(value=150)"}},
        tasktype = 1,
        type = 3,
    },

    [31014] = {
        achievetype = 1,
        condition = "캐릭터 스킬 3레벨 횟수=1",
        degreetype = 2,
        describe = "캐릭터 임의의 스킬 3레벨로 올라갑니다",
        id = 31014,
        name = "스킬 증가",
        open_id = 102,
        pre_condition = {"업적 퀘스트 완료=31028", "캐릭터 레벨=40"},
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=12000)"}},
        tasktype = 1,
        type = 3,
    },

    [31029] = {
        achievetype = 1,
        condition = "정령 사냥 횟수=10",
        degreetype = 2,
        describe = "정령 사냥 10회 진행",
        id = 31029,
        name = "정령 사냥",
        open_id = 1055,
        pre_condition = {"업적 퀘스트 완료=31014", "캐릭터 레벨=41"},
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=20000)"}},
        tasktype = 1,
        type = 3,
    },

    [31019] = {
        achievetype = 1,
        condition = "제국호송의 퀘스트 완료 횟수=2",
        degreetype = 2,
        describe = "제두 택배의 퀘스트 완료 2회",
        id = 31019,
        name = "호송",
        open_id = 1037,
        pre_condition = {"업적 퀘스트 완료=31029", "캐릭터 레벨=43"},
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=18000)"}},
        tasktype = 1,
        type = 3,
    },

    [31021] = {
        achievetype = 1,
        condition = "지하 감옥 클리어 층수=5",
        degreetype = 1,
        describe = "지하 감옥 제5층 클리어",
        id = 31021,
        name = "지하감옥",
        open_id = 1003,
        pre_condition = {"업적 퀘스트 완료=31019", "캐릭터 레벨=45"},
        rewarditem = {{["num"] = 5, ["sid"] = "11101"}},
        tasktype = 1,
        type = 3,
    },

    [31023] = {
        achievetype = 1,
        condition = "몽염 던전 클리어 횟수=1",
        degreetype = 1,
        describe = "몽염 사냥  클리어",
        id = 31023,
        name = "몽염사냥",
        open_id = 1029,
        pre_condition = {"업적 퀘스트 완료=31021", "캐릭터 레벨=50"},
        rewarditem = {{["num"] = 1, ["sid"] = "13213"}},
        tasktype = 1,
        type = 3,
    },

    [31025] = {
        achievetype = 1,
        condition = "인간의 형상 토벌=1",
        degreetype = 1,
        describe = "인간의 형상 토벌 1회 도전",
        id = 31025,
        name = "인간형상",
        open_id = 1045,
        pre_condition = {"업적 퀘스트 완료=31023", "캐릭터 레벨=55"},
        rewarditem = {{["num"] = 3, ["sid"] = "11010"}},
        tasktype = 1,
        type = 3,
    },

    [31024] = {
        achievetype = 1,
        condition = "NPC 도전=1",
        degreetype = 2,
        describe = "임의의 1개 도감 인물 도전 성공",
        id = 31024,
        name = "도감도전",
        open_id = 1027,
        pre_condition = {"업적 퀘스트 완료=31025", "캐릭터 레벨=60"},
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=10000)"}},
        tasktype = 1,
        type = 3,
    },

    [31524] = {
        achievetype = 2,
        condition = "룬 장착=1",
        degreetype = 2,
        describe = "룬 1개 장착",
        id = 31524,
        name = "룬장착",
        open_id = 203,
        pre_condition = {"캐릭터 레벨=5"},
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=5000)"}},
        tasktype = 1,
        type = 3,
    },

    [31525] = {
        achievetype = 2,
        condition = "룬 세트 장착=1",
        degreetype = 2,
        describe = "파트너 1명을 위해 룬 4개를 장착합니다",
        id = 31525,
        name = "룬장착",
        open_id = 203,
        pre_condition = {},
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=5000)"}},
        tasktype = 1,
        type = 3,
    },

    [31502] = {
        achievetype = 2,
        condition = "제1장 첫번째 보물 수령=1",
        degreetype = 1,
        describe = "제1장의 12성 보물 상자 수령",
        id = 31502,
        name = "보물상자",
        open_id = 1035,
        pre_condition = {},
        rewarditem = {{["num"] = 1, ["sid"] = "20302"}},
        tasktype = 1,
        type = 3,
    },

    [31533] = {
        achievetype = 2,
        condition = "룬 장착=2",
        degreetype = 2,
        describe = "룬 2개 장착",
        id = 31533,
        name = "룬장착",
        open_id = 203,
        pre_condition = {"업적 퀘스트 완료=31524", "캐릭터 레벨=7"},
        rewarditem = {{["num"] = 5, ["sid"] = "14021"}},
        tasktype = 1,
        type = 3,
    },

    [31526] = {
        achievetype = 2,
        condition = "3레벨 룬=1",
        degreetype = 2,
        describe = "임의의 룬이 3레벨 됩니다",
        id = 31526,
        name = "룬강화",
        open_id = 203,
        pre_condition = {"업적 퀘스트 완료=31533"},
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=5000)"}},
        tasktype = 1,
        type = 3,
    },

    [31527] = {
        achievetype = 2,
        condition = "룬 세트 장착=2",
        degreetype = 2,
        describe = "파트너 2명을 위해 룬 4개를 장착합니다",
        id = 31527,
        name = "룬장착",
        open_id = 203,
        pre_condition = {},
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=5000)"}},
        tasktype = 1,
        type = 3,
    },

    [31534] = {
        achievetype = 2,
        condition = "룬 장착=3",
        degreetype = 2,
        describe = "룬 3개 장착",
        id = 31534,
        name = "룬장착",
        open_id = 203,
        pre_condition = {"업적 퀘스트 완료=31526", "캐릭터 레벨=9"},
        rewarditem = {{["num"] = 8, ["sid"] = "14021"}},
        tasktype = 1,
        type = 3,
    },

    [31506] = {
        achievetype = 2,
        condition = "제1장 두번째 보물 수령=1",
        degreetype = 1,
        describe = "제1장의 24성 보물 상자를 수령합니다",
        id = 31506,
        name = "보물상자",
        open_id = 1035,
        pre_condition = {},
        rewarditem = {{["num"] = 1, ["sid"] = "20302"}},
        tasktype = 1,
        type = 3,
    },

    [31528] = {
        achievetype = 2,
        condition = "3레벨 룬=2",
        degreetype = 2,
        describe = "임의의 2개 룬 레벨업+3",
        id = 31528,
        name = "룬강화",
        open_id = 203,
        pre_condition = {"업적 퀘스트 완료=31534"},
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=5000)"}},
        tasktype = 1,
        type = 3,
    },

    [31529] = {
        achievetype = 2,
        condition = "룬 세트 장착=3",
        degreetype = 2,
        describe = "파트너 3명을 위해 룬 4개를 장착합니다",
        id = 31529,
        name = "룬장착",
        open_id = 203,
        pre_condition = {},
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=5000)"}},
        tasktype = 1,
        type = 3,
    },

    [31510] = {
        achievetype = 2,
        condition = "업적 보상 수령=1",
        degreetype = 1,
        describe = "업적 보상 1회 수령",
        id = 31510,
        name = "업적 보상",
        open_id = 1016,
        pre_condition = {"업적 퀘스트 완료=31528", "캐릭터 레벨=13"},
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=5000)"}},
        tasktype = 1,
        type = 3,
    },

    [31530] = {
        achievetype = 2,
        condition = "룬 세트 장착=4",
        degreetype = 2,
        describe = "파트너 4명을 위해 룬 4개를 장착합니다",
        id = 31530,
        name = "룬장착",
        open_id = 203,
        pre_condition = {},
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=5000)"}},
        tasktype = 1,
        type = 3,
    },

    [31535] = {
        achievetype = 2,
        condition = "룬 장착=4",
        degreetype = 2,
        describe = "룬 4개 장착",
        id = 31535,
        name = "룬장착",
        open_id = 203,
        pre_condition = {"업적 퀘스트 완료=31510"},
        rewarditem = {{["num"] = 15, ["sid"] = "14021"}},
        tasktype = 1,
        type = 3,
    },

    [31531] = {
        achievetype = 2,
        condition = "3레벨 룬=4",
        degreetype = 2,
        describe = "임의의 4개 룬 레벨업+3",
        id = 31531,
        name = "룬강화",
        open_id = 203,
        pre_condition = {"업적 퀘스트 완료=31535"},
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=5000)"}},
        tasktype = 1,
        type = 3,
    },

    [31515] = {
        achievetype = 2,
        condition = "교류하기=2",
        degreetype = 2,
        describe = "저택의 중화 쓰다듬기 2회",
        id = 31515,
        name = "저택",
        open_id = 1004,
        pre_condition = {"업적 퀘스트 완료=31531", "캐릭터 레벨=20"},
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=8000)"}},
        tasktype = 1,
        type = 3,
    },

    [31516] = {
        achievetype = 2,
        condition = "특훈=1",
        degreetype = 2,
        describe = "저택의 중화 오락 1회 시작",
        id = 31516,
        name = "저택오락",
        open_id = 1004,
        pre_condition = {"업적 퀘스트 완료=31515"},
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=8000)"}},
        tasktype = 1,
        type = 3,
    },

    [31518] = {
        achievetype = 2,
        condition = "다과회 몬스터 처치 횟수=1",
        degreetype = 1,
        describe = "야옹이 다과회 전투에서 1회 승리",
        id = 31518,
        name = "다과회",
        open_id = 1023,
        pre_condition = {"업적 퀘스트 완료=31516", "캐릭터 레벨=38"},
        rewarditem = {{["num"] = 1, ["sid"] = "14002"}},
        tasktype = 1,
        type = 3,
    },

    [31519] = {
        achievetype = 2,
        condition = "탐색=20",
        degreetype = 2,
        describe = "20 탐색 포인트 소모",
        id = 31519,
        name = "탐색",
        open_id = 1011,
        pre_condition = {"업적 퀘스트 완료=31518", "캐릭터 레벨=40"},
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=20000)"}},
        tasktype = 1,
        type = 3,
    },

    [31532] = {
        achievetype = 2,
        condition = "장착한 어령 수=4",
        degreetype = 2,
        describe = "어령 4개 장착",
        id = 31532,
        name = "어령장착",
        open_id = 205,
        pre_condition = {"업적 퀘스트 완료=31519", "캐릭터 레벨=41"},
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=5000)"}},
        tasktype = 1,
        type = 3,
    },

    [31520] = {
        achievetype = 2,
        condition = "이공유배 제1층 클리어 횟수=1",
        degreetype = 2,
        describe = "어령 던전 1층 클리어",
        id = 31520,
        name = "어령던전",
        open_id = 1040,
        pre_condition = {"업적 퀘스트 완료=31532", "캐릭터 레벨=42"},
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=8000)"}},
        tasktype = 1,
        type = 3,
    },

    [31521] = {
        achievetype = 2,
        condition = "파트너 원정 시작 횟수=1",
        degreetype = 2,
        describe = "파트너 원정 1회 시작",
        id = 31521,
        name = "원정",
        open_id = 1013,
        pre_condition = {"업적 퀘스트 완료=31520", "캐릭터 레벨=44"},
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=20000)"}},
        tasktype = 1,
        type = 3,
    },

    [31522] = {
        achievetype = 2,
        condition = "파트너 각성 횟수=1",
        degreetype = 2,
        describe = "임의의 파트너 각성",
        id = 31522,
        name = "각성",
        open_id = 204,
        pre_condition = {"업적 퀘스트 완료=31521", "캐릭터 레벨=45"},
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=20000)"}},
        tasktype = 1,
        type = 3,
    },

    [31523] = {
        achievetype = 2,
        condition = "월견에서 제2차 몬스터 처치=1",
        degreetype = 2,
        describe = "월견 던전 최소 2차 클리어",
        id = 31523,
        name = "월견던전",
        open_id = 1007,
        pre_condition = {"업적 퀘스트 완료=31522", "캐릭터 레벨=46"},
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=20000)"}},
        tasktype = 1,
        type = 3,
    },

}
