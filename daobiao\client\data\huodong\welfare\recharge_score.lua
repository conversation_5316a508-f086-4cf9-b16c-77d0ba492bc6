-- ./excel/huodong/welfare/recharge_score.xlsx
return {

    [1] = {
        buy_limit = 0,
        desc = "뚜뚜루조각 획득",
        id = 1,
        name = "뚜뚜루조각",
        open = 1,
        point = 60,
        reward = {["num"] = 1, ["sid"] = "20418"},
    },

    [2] = {
        buy_limit = 0,
        desc = "영월조각 획득",
        id = 2,
        name = "영월조각",
        open = 1,
        point = 60,
        reward = {["num"] = 1, ["sid"] = "20405"},
    },

    [3] = {
        buy_limit = 30,
        desc = "무기 외 원석 1개 획득",
        id = 3,
        name = "원석상자",
        open = 1,
        point = 20,
        reward = {["num"] = 1, ["sid"] = "13213"},
    },

    [4] = {
        buy_limit = 50,
        desc = "영혼상자 오픈에 사용",
        id = 4,
        name = "영혼열쇠",
        open = 1,
        point = 30,
        reward = {["num"] = 1, ["sid"] = "10040"},
    },

    [5] = {
        buy_limit = 100,
        desc = "무기단조에 사용",
        id = 5,
        name = "장비원석",
        open = 1,
        point = 20,
        reward = {["num"] = 1, ["sid"] = "11010"},
    },

    [6] = {
        buy_limit = 50,
        desc = "랜덤 파트너 조각 획득",
        id = 6,
        name = "랜덤조각",
        open = 1,
        point = 60,
        reward = {["num"] = 1, ["sid"] = "13260"},
    },

    [7] = {
        buy_limit = 10,
        desc = "무기단조에 사용",
        id = 7,
        name = "무기원석",
        open = 1,
        point = 60,
        reward = {["num"] = 1, ["sid"] = "11004"},
    },

    [8] = {
        buy_limit = 0,
        desc = "1만골드 획득",
        id = 8,
        name = "골드패키지",
        open = 1,
        point = 5,
        reward = {["num"] = 1, ["sid"] = "13306"},
    },

    [9] = {
        buy_limit = 30,
        desc = "만능조각 획득",
        id = 9,
        name = "마스터조각",
        open = 1,
        point = 80,
        reward = {["num"] = 1, ["sid"] = "14002"},
    },

    [10] = {
        buy_limit = 180,
        desc = "파트너 스킬강화에 사용",
        id = 10,
        name = "카라멜진빵",
        open = 1,
        point = 10,
        reward = {["num"] = 1, ["sid"] = "14011"},
    },

    [11] = {
        buy_limit = 30,
        desc = "무기 외 원석 1개 획득",
        id = 11,
        name = "원석상자",
        open = 1,
        point = 20,
        reward = {["num"] = 1, ["sid"] = "13213"},
    },

    [12] = {
        buy_limit = 50,
        desc = "영혼상자 오픈에 사용",
        id = 12,
        name = "영혼열쇠",
        open = 1,
        point = 30,
        reward = {["num"] = 1, ["sid"] = "10040"},
    },

    [13] = {
        buy_limit = 100,
        desc = "무기단조에 사용",
        id = 13,
        name = "장비원석",
        open = 1,
        point = 20,
        reward = {["num"] = 1, ["sid"] = "11010"},
    },

    [14] = {
        buy_limit = 50,
        desc = "랜덤 파트너 조각 획득",
        id = 14,
        name = "랜덤조각",
        open = 1,
        point = 60,
        reward = {["num"] = 1, ["sid"] = "13260"},
    },

    [15] = {
        buy_limit = 10,
        desc = "무기단조에 사용",
        id = 15,
        name = "무기원석",
        open = 1,
        point = 60,
        reward = {["num"] = 1, ["sid"] = "11004"},
    },

    [16] = {
        buy_limit = 0,
        desc = "1만골드 획득",
        id = 16,
        name = "골드패키지",
        open = 1,
        point = 5,
        reward = {["num"] = 1, ["sid"] = "13306"},
    },

}
