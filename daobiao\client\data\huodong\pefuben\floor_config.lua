-- ./excel/huodong/pefuben/fuben.xlsx
return {

    [1] = {
        fail_cost = 3,
        floor = 1,
        reset_cost = 5000,
        sweep_cost = 2,
        tili_cost = 12,
        turn_limit = 5,
    },

    [2] = {
        fail_cost = 3,
        floor = 2,
        reset_cost = 5000,
        sweep_cost = 2,
        tili_cost = 12,
        turn_limit = 5,
    },

    [3] = {
        fail_cost = 3,
        floor = 3,
        reset_cost = 5000,
        sweep_cost = 2,
        tili_cost = 12,
        turn_limit = 30,
    },

    [4] = {
        fail_cost = 3,
        floor = 4,
        reset_cost = 10000,
        sweep_cost = 2,
        tili_cost = 12,
        turn_limit = 30,
    },

    [5] = {
        fail_cost = 3,
        floor = 5,
        reset_cost = 10000,
        sweep_cost = 2,
        tili_cost = 12,
        turn_limit = 30,
    },

    [6] = {
        fail_cost = 3,
        floor = 6,
        reset_cost = 10000,
        sweep_cost = 2,
        tili_cost = 12,
        turn_limit = 30,
    },

    [7] = {
        fail_cost = 3,
        floor = 7,
        reset_cost = 15000,
        sweep_cost = 2,
        tili_cost = 12,
        turn_limit = 30,
    },

    [8] = {
        fail_cost = 3,
        floor = 8,
        reset_cost = 15000,
        sweep_cost = 2,
        tili_cost = 12,
        turn_limit = 30,
    },

    [9] = {
        fail_cost = 3,
        floor = 9,
        reset_cost = 15000,
        sweep_cost = 2,
        tili_cost = 12,
        turn_limit = 30,
    },

    [10] = {
        fail_cost = 3,
        floor = 10,
        reset_cost = 20000,
        sweep_cost = 2,
        tili_cost = 12,
        turn_limit = 30,
    },

    [11] = {
        fail_cost = 3,
        floor = 11,
        reset_cost = 20000,
        sweep_cost = 2,
        tili_cost = 12,
        turn_limit = 30,
    },

}
