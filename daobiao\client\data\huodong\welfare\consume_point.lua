-- ./excel/huodong/welfare/consume_point.xlsx
return {

    [100004] = {
        buy_limit = 0,
        desc = "장비쉬령에 사용",
        id = 100004,
        name = "쉬령운정",
        open = 1,
        point = 125,
        reward = {["num"] = 1, ["sid"] = "11101"},
    },

    [100005] = {
        buy_limit = 0,
        desc = "장비단조에 사용",
        id = 100005,
        name = "장비원석",
        open = 1,
        point = 1250,
        reward = {["num"] = 1, ["sid"] = "11010"},
    },

    [100006] = {
        buy_limit = 0,
        desc = "무기 외 원석 1개 획득",
        id = 100006,
        name = "원석상자",
        open = 1,
        point = 1875,
        reward = {["num"] = 1, ["sid"] = "13213"},
    },

    [100007] = {
        buy_limit = 0,
        desc = "영혼상자 오픈에 사용",
        id = 100007,
        name = "영혼열쇠",
        open = 1,
        point = 1875,
        reward = {["num"] = 1, ["sid"] = "10040"},
    },

    [100002] = {
        buy_limit = 80,
        desc = "호현조각 획득",
        id = 100002,
        name = "호현조각",
        open = 1,
        point = 1000,
        reward = {["num"] = 1, ["sid"] = "20415"},
    },

    [100001] = {
        buy_limit = 80,
        desc = "연조각 획득",
        id = 100001,
        name = "연조각",
        open = 1,
        point = 1000,
        reward = {["num"] = 1, ["sid"] = "20407"},
    },

    [100008] = {
        buy_limit = 0,
        desc = "무기단조에 사용",
        id = 100008,
        name = "무기원석",
        open = 1,
        point = 3750,
        reward = {["num"] = 1, ["sid"] = "11004"},
    },

    [100003] = {
        buy_limit = 80,
        desc = "견요조각 획득",
        id = 100003,
        name = "견요조각",
        open = 1,
        point = 1000,
        reward = {["num"] = 1, ["sid"] = "20409"},
    },

    [100009] = {
        buy_limit = 30,
        desc = "만능조각 획득",
        id = 100009,
        name = "만능조각",
        open = 1,
        point = 2000,
        reward = {["num"] = 1, ["sid"] = "14002"},
    },

    [100010] = {
        buy_limit = 100,
        desc = "무기단조에 사용",
        id = 100010,
        name = "장비원석",
        open = 1,
        point = 500,
        reward = {["num"] = 1, ["sid"] = "11010"},
    },

    [100011] = {
        buy_limit = 30,
        desc = "무기 외 원석 1개 획득",
        id = 100011,
        name = "원석상자",
        open = 1,
        point = 500,
        reward = {["num"] = 1, ["sid"] = "13213"},
    },

    [100012] = {
        buy_limit = 50,
        desc = "영혼상자 오픈에 사용",
        id = 100012,
        name = "영혼열쇠",
        open = 1,
        point = 750,
        reward = {["num"] = 1, ["sid"] = "10040"},
    },

    [100013] = {
        buy_limit = 180,
        desc = "파트너 스킬 강화에 사용",
        id = 100013,
        name = "카라멜진빵",
        open = 1,
        point = 250,
        reward = {["num"] = 1, ["sid"] = "14011"},
    },

    [100014] = {
        buy_limit = 50,
        desc = "랜덤 파트너 조각 획득",
        id = 100014,
        name = "파트너조각",
        open = 1,
        point = 1500,
        reward = {["num"] = 1, ["sid"] = "13260"},
    },

    [100015] = {
        buy_limit = 10,
        desc = "무기단조에 사용",
        id = 100015,
        name = "무기원석",
        open = 1,
        point = 1500,
        reward = {["num"] = 1, ["sid"] = "11004"},
    },

    [100016] = {
        buy_limit = 0,
        desc = "1만골드 획득",
        id = 100016,
        name = "1만골드",
        open = 1,
        point = 125,
        reward = {["num"] = 1, ["sid"] = "13306"},
    },

}
