-- ./excel/huodong/question/question.xlsx
return {

    [54] = {
        answer = {"오매불망", "각주구검", "전전긍긍", "연연불망"},
        id = 54,
        question = '다음 성어 중 "자나 깨나 잊지 못함"을 뜻하는 성어는?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [55] = {
        answer = {"유언비어", "과유불급", "호가호위", "일취월장"},
        id = 55,
        question = '다음 성어 중 "아무 근거 없이 널리 퍼진 소문"을 뜻하는 성어는?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [56] = {
        answer = {"등용문", "금의환향", "입신양명", "유방백세"},
        id = 56,
        question = '다음 성어 중 "입신출세의 관문"을 뜻하는 성어는?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [57] = {
        answer = {"도광양회", "일편단심", "칠전팔기", "파죽지세"},
        id = 57,
        question = '"재능과 명성을 드러내지 않고 참고 기다린다"를 뜻하는 성어는?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [58] = {
        answer = {"장유유서", "부위자강", "일편단심", "군신유의"},
        id = 58,
        question = '"어른과 어린이 사이에는 순서와 질서가 있음"을 뜻하는 성어는?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [59] = {
        answer = {"죽마고우", "관포지교", "간담상조", "문경지교"},
        id = 59,
        question = '"대나무 말을 타고 놀던 가까운 친구"를 뜻하는 성어는?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [77] = {
        answer = {"오동조조", "삼고초려", "도원결의", "괄목상대"},
        id = 77,
        question = "다음 성어 중「삼국지」와 무관한 성어는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [78] = {
        answer = {"아리스토텔레스", "공자", "예수", "석가모니"},
        id = 78,
        question = "다음 중 4대 성인에 속하지 않는 인물은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [79] = {
        answer = {"광해군", "정조", "영조", "태종"},
        id = 79,
        question = "인조반정으로 폐위된 조선의 제16대 왕은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [80] = {
        answer = {"영조", "태종", "세종", "단종"},
        id = 80,
        question = "다음 중 조선 시대에 가장 장수한 왕은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [81] = {
        answer = {"인종", "예종", "정종", "중종"},
        id = 81,
        question = "다음 중 가장 짧은 기간 동안 재위한 조선 왕은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [82] = {
        answer = {"헌종", "순조", "철종", "단종"},
        id = 82,
        question = "다음 중 가장 이른 나이에 왕위에 오른 조선 왕은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [83] = {
        answer = {"강감찬", "이순신", "최영", "곽재우"},
        id = 83,
        question = "다음 인물 중 가장 먼저 태어난 인물은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [84] = {
        answer = {"귀주대첩", "임진왜란", "병자호란", "6.25 전쟁"},
        id = 84,
        question = "다음 사건 중 가장 먼저 발생한 사건은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [85] = {
        answer = {"마동석", "이병헌", "송강호", "최민식"},
        id = 85,
        question = "다음 중 한국인이 아닌 사람은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [86] = {
        answer = {"관상", "겨울왕국 2", "베테랑", "암살"},
        id = 86,
        question = "다음 중 천만 관객 영화가 아닌 것은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [87] = {
        answer = {"의원내각제", "입법", "사법", "행정"},
        id = 87,
        question = "대한민국 삼권분립 체제에 해당하지 않는 것은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [88] = {
        answer = {"하늘", "땅", "저승", "우주"},
        id = 88,
        question = "그리스 신화 속의 제우스는 무엇을 관장하나요?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [89] = {
        answer = {"최적 사용기", "제조일자", "출하일자", "판매 기한"},
        id = 89,
        question = "식품의 유통기한은 무엇을 가리키나?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [90] = {
        answer = {"28일", "100일", "6개월", "3개월"},
        id = 90,
        question = "아기는 생후 며칠까지가 신생아인가?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [91] = {
        answer = {"관성", "만유인력", "가속도", "원심력"},
        id = 91,
        question = "뉴턴 제1법칙은 무엇인가?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [92] = {
        answer = {"바티칸", "리히텐슈타인", "모나코", "수단"},
        id = 92,
        question = "다음 중 가장 작은 나라는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [93] = {
        answer = {"CPU", "GPU", "SDK", "IOI"},
        id = 93,
        question = "컴퓨터의 중앙 처리 장치를 영문으로는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [94] = {
        answer = {"프랑스", "영국", "독일", "미국"},
        id = 94,
        question = "칸 영화제는 어느 나라에서 열리나?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [95] = {
        answer = {"금성", "수성", "지구", "목성"},
        id = 95,
        question = "태양이랑 두 번째 가까운 행성은 무엇인가?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [96] = {
        answer = {"콩비지", "콩나물", "킹콩", "콩자반"},
        id = 96,
        question = "[넌센스] 콩이 바쁘면?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [97] = {
        answer = {"인삼차", "레몬차", "둥굴레차", "고삼차"},
        id = 97,
        question = "[넌센스] 세 사람만 탈 수 있는 차는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [98] = {
        answer = {"서경", "논어", "중용", "대학"},
        id = 98,
        question = "사서삼경의 사서가 아닌 것은 무엇입니까?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [99] = {
        answer = {"마야", "인더스", "이집트", "황하"},
        id = 99,
        question = "세계 4대 문명이 발생한 지역이 아닌 것은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [100] = {
        answer = {"날벼락", "호랑이", "먹구름", "도깨비"},
        id = 100,
        question = "마른하늘에 OOO의 OOO은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [101] = {
        answer = {"포도청", "레몬청", "좌포청", "우포청"},
        id = 101,
        question = "목구멍이 OOO의 OOO은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [102] = {
        answer = {"座", "左", "佐", "坐"},
        id = 102,
        question = '"좌우명"이라는 단어에서 "좌"에 해당하는 한자는?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [103] = {
        answer = {"짜장면", "김밥", "삼겹살", "비빔밥"},
        id = 103,
        question = "4월 14일은 무슨 음식을 먹는 날인가요?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [104] = {
        answer = {"부산", "광주", "인천", "고양"},
        id = 104,
        question = "다음 중 인구가 가장 많은 도시는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [105] = {
        answer = {"금고아", "머리띠", "헤드셋", "밀짚모자"},
        id = 105,
        question = "「서유기」에서 손오공이 머리에 두르고 있는 물건은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [106] = {
        answer = {"5", "1", "3", "12"},
        id = 106,
        question = "가정의 달은 몇 월일까요?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [107] = {
        answer = {"홀인원", "홈런", "덩크", "스핀"},
        id = 107,
        question = "골프에서 티샷이 단번이 홀에 들어가는 것은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [108] = {
        answer = {"아폴로 11호", "나로호", "누리호", "텐궁"},
        id = 108,
        question = "인류 역사 최초로 달에 착륙한 유인우주선은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [109] = {
        answer = {"냉전", "게릴라전", "육탄전", "해물파전"},
        id = 109,
        question = "2차 세계 대전 이후 미국과 소련의 적대 관계를 표현하는 단어는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [110] = {
        answer = {"아프로디테", "헤라", "아테네", "아르테미스"},
        id = 110,
        question = "그리스 신화에서 트로이 왕자 파리스에게 황금 사과를 받은 여신은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [111] = {
        answer = {"이카루스", "헤파이토스", "헤라클레스", "프로메테우스"},
        id = 111,
        question = "그리스 신화 속 다이달로스가 만든 날개를 달고 날다 죽은 사람은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [112] = {
        answer = {"뱀", "호랑이", "독수리", "박쥐"},
        id = 112,
        question = "세계 보건 기구 WHO의 엠블럼에 그려져 있는 동물은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [113] = {
        answer = {"MBTI", "에고그램", "성격유형론", "길퍼드검사"},
        id = 113,
        question = "일일생활에 활용할 수 있도록 고안된 자기 보고식 성격 유형 지표는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [114] = {
        answer = {"마드리드", "바르셀로나", "세비야", "발렌시아"},
        id = 114,
        question = "스페인의 수도는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [115] = {
        answer = {"이산화탄소", "수소", "산소", "규소"},
        id = 115,
        question = "원소기호 CO2는 무엇인가요?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [116] = {
        answer = {"애드리브", "액션", "프리덤", "칸타레"},
        id = 116,
        question = '"자유롭게"라는 뜻의 라틴어로 대본에 없는 대사를 뜻하는 말은?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [117] = {
        answer = {"베토벤", "쇼팽", "차이코프스키", "헨델"},
        id = 117,
        question = '피아노곡 "엘리제를 위하여"를 만든 작곡가는?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [118] = {
        answer = {"생텍쥐페리", "톨스토이", "헤밍웨이", "안데르센"},
        id = 118,
        question = "「어린왕자」의 저자는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [119] = {
        answer = {"<코스모스>", "<데미안>", "<변신>", "<반지의 제왕>"},
        id = 119,
        question = "다음 중 천문학자 칼 세이건의 저서는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [120] = {
        answer = {"왕의 남자", "패션왕", "신과함께", "내부자들"},
        id = 120,
        question = "다음 중 웹툰 원작 영화가 아닌 것은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [121] = {
        answer = {"이중섭", "나혜석", "신사임당", "한석봉"},
        id = 121,
        question = "20세기 대한민국 화가로 「싸우는 소」「흰 소」등을 그린 화가는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [122] = {
        answer = {"잭 스패로우", "프로도", "골룸", "레골라스"},
        id = 122,
        question = "「반지의 제왕」 등장인물이 아닌 사람은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [123] = {
        answer = {"미국", "러시아", "호주", "독일"},
        id = 123,
        question = "세계에서 세 번째로 인구가 많은 나라는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [124] = {
        answer = {"미국 국립 박물관", "루브르 박물관", "바티칸 박물관", "영국 박물관"},
        id = 124,
        question = "세계 3대 박물관이 아닌 박물관은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [149] = {
        answer = {"어린 왕자", "연금술사", "빨간 머리 앤", "전쟁과 평화"},
        id = 149,
        question = "다음 중 전 세계 1억 부 이상 판매된 책은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [150] = {
        answer = {"니체", "데카르트", "플라톤", "벤담"},
        id = 150,
        question = '"신은 죽었다"라는 말을 한 철학자는?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [151] = {
        answer = {"상아", "뿔", "어금니", "오돌뼈"},
        id = 151,
        question = "코끼리의 송곳니를 지칭하는 말은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [152] = {
        answer = {"이승만", "안중근", "김구", "최규하"},
        id = 152,
        question = "우리나라 초대 대통령은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [153] = {
        answer = {"히틀러", "링컨", "장제스", "간디"},
        id = 153,
        question = "다음 중 1차 세계 대전 참전 인물은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [154] = {
        answer = {"6", "7", "5", "4"},
        id = 154,
        question = "우리나라는 몇 개의 광역시로 이루어져 있나요?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [155] = {
        answer = {"꺼병이", "병아리", "애송이", "고도리"},
        id = 155,
        question = "꿩의 새끼는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [156] = {
        answer = {"들국화", "송골매", "샌드페블즈", "무한궤도"},
        id = 156,
        question = "「매일 그대와」,「행진」을 부른 가수는?「」",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [157] = {
        answer = {"82", "86", "101", "136"},
        id = 157,
        question = "한국의 국가 전화번호는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [158] = {
        answer = {"로미오와줄리엣", "햄릿", "오셀로", "리어왕"},
        id = 158,
        question = "셰익스피어의 4대 비극이 아닌 것은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [159] = {
        answer = {"초록", "빨강", "노랑", "파랑"},
        id = 159,
        question = "색의 3원색이 아닌 것은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [160] = {
        answer = {"매미", "파리", "모기", "귀뚜라미"},
        id = 160,
        question = "굼벵이는 어떤 곤충의 애벌레인가요?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [161] = {
        answer = {"펠레스코어", "메시스코어", "베컴스코어", "지단스코어"},
        id = 161,
        question = "축구에서 가장 흥미진진한 게임스코어를 이르는 말은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [162] = {
        answer = {"개골산", "봉래산", "풍악산", "금설산"},
        id = 162,
        question = "다음 중 금강산의 겨울 이름은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [163] = {
        answer = {"아르헨티나", "브라질", "칠레", "멕시코"},
        id = 163,
        question = '춤 "탱고"의 고장은?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [164] = {
        answer = {"영국", "미국", "홍콩", "호주"},
        id = 164,
        question = '"BUS" 라는 단어를 처음 사용한 나라는?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [165] = {
        answer = {"아버지", "어머니", "삼촌", "아들"},
        id = 165,
        question = '"아바이 순대"에서 "아바이"의 뜻은?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [166] = {
        answer = {"상하이", "하얼빈", "칭다오", "연경"},
        id = 166,
        question = "다음 도시 중 맥주 브랜드 이름이 아닌 도시는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [167] = {
        answer = {"장영실", "박연", "정약용", "정인지"},
        id = 167,
        question = "자격루를 발명한 사람은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [168] = {
        answer = {"이방원", "정몽주", "이성계", "정종"},
        id = 168,
        question = "고려시대 시, 하여가(何如歌)를 지은 인물은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [169] = {
        answer = {"애드리브", "액션", "프리덤", "칸타레"},
        id = 169,
        question = '"자유롭게"라는 뜻의 라틴어로 대본에 없는 대사를 뜻하는 말은?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [170] = {
        answer = {"파리", "아테네", "런던", "베를린"},
        id = 170,
        question = "1924년 올림픽 개최지는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [171] = {
        answer = {"비타민C", "비타민E", "비타민D", "철분"},
        id = 171,
        question = "괴혈병은 무엇이 부족해서 생길까요?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [172] = {
        answer = {"아우구스투스", "막시무스", "네로", "카이사르"},
        id = 172,
        question = "고대 로마의 최초 황제 이름은 무엇일까요?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [173] = {
        answer = {"홍길동전", "춘향전", "태백산맥", "토지"},
        id = 173,
        question = "우리나라 최초의 한글 소설은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [174] = {
        answer = {"얼음보숭이", "냉동우유", "단 얼음", "아이스께끼"},
        id = 174,
        question = "아이스크림을 뜻하는 북한말은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [175] = {
        answer = {"108", "56", "84", "102"},
        id = 175,
        question = "야구공의 실밥은 총 몇 개일까요?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [176] = {
        answer = {"주황", "빨강", "노랑", "초록"},
        id = 176,
        question = "올림픽 오륜기의 색이 아닌 것은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [177] = {
        answer = {"켤레", "벌", "짝", "궤"},
        id = 177,
        question = "신발을 세는 단위는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [178] = {
        answer = {"4", "3", "5", "2"},
        id = 178,
        question = "1+2 곱하기 3-6 나누기 2 = ？",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [179] = {
        answer = {"19", "18", "17", "20"},
        id = 179,
        question = "16-15 나누기 3+4 곱하기 2 = ？",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [180] = {
        answer = {"제비", "까치", "독수리", "까마귀"},
        id = 180,
        question = "우체국 로고에 있는 새는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [181] = {
        answer = {"나트륨", "탄수화물", "지방", "단백질"},
        id = 181,
        question = "우리 몸에 3대 영양소가 아닌 것은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [182] = {
        answer = {"조지 워싱턴", "링컨", "루즈벨트", "케네디"},
        id = 182,
        question = "1달러에 그려져 있는 인물은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [183] = {
        answer = {"경칩", "곡우", "청명", "입춘"},
        id = 183,
        question = "개구리가 겨울잠에서 깨는 시기라고 하는 24절기 중 세 번째 절기는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [184] = {
        answer = {"0", "1", "186", "1130"},
        id = 184,
        question = "전화기에 있는 숫자를 모두 곱하면?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [185] = {
        answer = {"코르크", "반달구", "원구", "고무구"},
        id = 185,
        question = "배드민턴에 사용되는 셔틀콕에 박힌 반구 모양의 이름은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [186] = {
        answer = {"4", "6", "2", "8"},
        id = 186,
        question = "바이올린의 현은 몇 개로 되어있을까요?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [187] = {
        answer = {"얼룩말", "당나귀", "물소", "캥거루"},
        id = 187,
        question = "줄말 이라고 불리는 동물은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [188] = {
        answer = {"그리스", "미국", "터키", "이집트"},
        id = 188,
        question = "그릭요거트를 처음 만든 나라는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [189] = {
        answer = {"초록색", "주황색", "보라색", "회색"},
        id = 189,
        question = "노란색과 파란색을 섞으면 무슨 색이 나올까요?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [190] = {
        answer = {"애드리브", "액션", "프리덤", "칸타레"},
        id = 190,
        question = '"자유롭게"라는 뜻의 라틴어로 대본에 없는 대사를 뜻하는 말은?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [191] = {
        answer = {"사촌", "애인", "친구", "원수"},
        id = 191,
        question = "OO이 땅을 사면 배 아프다, OO은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [192] = {
        answer = {"사과나무", "배나무", "대나무", "소나무"},
        id = 192,
        question = '동화 "아낌없이 주는 나무"의 나무는 어떤 나무일까요?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [193] = {
        answer = {"O", "A", "B", "AB"},
        id = 193,
        question = "모든 혈액형에게 피를 수혈 해줄 수 있는 혈액형은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [194] = {
        answer = {"모아이", "병마용", "삼신", "돌하르방"},
        id = 194,
        question = "이스터섬에 대량으로 발견된 석상 이름은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [195] = {
        answer = {"애드리브", "액션", "프리덤", "칸타레"},
        id = 195,
        question = '"자유롭게"라는 뜻의 라틴어로 대본에 없는 대사를 뜻하는 말은?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [196] = {
        answer = {"누룩", "메주", "누룽지", "락스"},
        id = 196,
        question = "곰팡이를 번식시키는 형태로 술을 만들 때 쓰는 발효제는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [197] = {
        answer = {"땅콩", "감자", "우엉", "생강"},
        id = 197,
        question = "다음 중 뿌리식물이 아닌 것은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [205] = {
        answer = {";", "[", '"', "&"},
        id = 205,
        question = "세미콜론이라고 불리는 문장 부호는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [206] = {
        answer = {"북", "동", "서", "남"},
        id = 206,
        question = "나침반에서 N극이 가리키는 방향은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [207] = {
        answer = {"스콜", "엘리뇨", "라니뇨", "태풍"},
        id = 207,
        question = "갑자기 바람이 불기 시작하여 몇 분간 지속 후 멈추는 현상은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [208] = {
        answer = {"3B정책", "3D정책", "3A정책", "3C정책"},
        id = 208,
        question = "19세기 말부터 1차 세계대전까지 독일의 제국주의적 근동정책은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [209] = {
        answer = {"벤망토킹", "리포지셔닝", "리엔지니어링", "산업스파이"},
        id = 209,
        question = "기업 경쟁력 향상을 위해 타사에서 혁신 기법을 배워오는 행위는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [210] = {
        answer = {"테니스", "농구", "승마", "펜싱"},
        id = 210,
        question = '"러브게임" 이란 단어가 사용되는 스포츠는?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [211] = {
        answer = {"피리", "징", "장구", "북"},
        id = 211,
        question = "우리나라 사물놀이에 쓰이는 악기가 아닌 것은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [212] = {
        answer = {"경인선", "경춘선", "호남선", "경부선"},
        id = 212,
        question = "우리나라에 최초로 놓인 철도는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [213] = {
        answer = {"베를린", "뮌헨", "라이프치히", "함부르크"},
        id = 213,
        question = "독일의 수도는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [214] = {
        answer = {"모차르트", "베토벤", "살리에리", "비발디"},
        id = 214,
        question = "「터키 행진곡」을 작곡한 음악가는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [215] = {
        answer = {"불의 잔", "혼혈 왕자", "비밀의 방", "마법사의 돌"},
        id = 215,
        question = "해리포터 시리즈 중 4번째 시리즈는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [216] = {
        answer = {"고흐", "고갱", "피카소", "클림트"},
        id = 216,
        question = "「별이 빛나는 밤」을 그린 화가는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [217] = {
        answer = {"리스본", "이스탄불", "바그다드", "포트빌라"},
        id = 217,
        question = "포르투갈의 수도는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [218] = {
        answer = {"튤립", "장미", "해바라기", "목란"},
        id = 218,
        question = "네덜란드 국화는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [219] = {
        answer = {"카르타고", "로마", "바빌론", "페르시아"},
        id = 219,
        question = "인류 역사상 최고의 명장 중 한 명인 한니발 바르카의 국가는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [220] = {
        answer = {"십자군 전쟁", "장미 전쟁", "포에니 전쟁", "월털루 전쟁"},
        id = 220,
        question = "1095년부터 1291년까지 간헐적으로 일어난 종교 전쟁은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [221] = {
        answer = {"하멜 포류기", "동방견문록", "걸리버 여행기", "난중일기"},
        id = 221,
        question = "태풍으로 조선에 포착한 네덜란드인들의 억류 생활을 기록한 책은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [222] = {
        answer = {"징비록", "백범일지", "한중록", "조선왕조실록"},
        id = 222,
        question = "류성룡 선생이 임진왜란이 발생 직전부터 조정의 이야기를 기록한 책은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [223] = {
        answer = {"애드리브", "액션", "프리덤", "칸타레"},
        id = 223,
        question = '"자유롭게"라는 뜻의 라틴어로 대본에 없는 대사를 뜻하는 말은?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [224] = {
        answer = {"이대", "홍대", "종로", "이태원"},
        id = 224,
        question = "우리나라 최초의 스타벅스 매장 위치는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [225] = {
        answer = {"미국", "중국", "일본", "독일"},
        id = 225,
        question = "대한민국의 공식적인 동맹국은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [226] = {
        answer = {"그린란드", "뉴기니", "보르네오", "마다가스카라"},
        id = 226,
        question = "세계에서 가장 큰 섬은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [227] = {
        answer = {"사하라", "파타고니아", "고비사막", "아라비아 사막"},
        id = 227,
        question = "세계에서 가장 큰 사막은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [228] = {
        answer = {"장미", "해바라기", "물망초", "라일락"},
        id = 228,
        question = "영국의 국화는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [229] = {
        answer = {"볍씨", "호박씨", "해바라기씨", "수박씨"},
        id = 229,
        question = '"귀신 씻나락 까먹는 소리" 에서 "씻나락" 은?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [230] = {
        answer = {"456", "218", "1", "67"},
        id = 230,
        question = "넷플릭스 드라마 「오징어 게임」에서 주인공 성기훈의 등번호는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [231] = {
        answer = {"빨강", "노랑", "하양", "초록"},
        id = 231,
        question = "루돌프 사슴 코의 색깔은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [232] = {
        answer = {"성균관", "집현전", "태학", "노량진"},
        id = 232,
        question = "조선시대 서울에 설치했던 최고의 교육기관은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [233] = {
        answer = {"브루투스", "가룟유다", "본디오 빌라도", "카인"},
        id = 233,
        question = "율리우스 카이사르를 죽인 인물은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [240] = {
        answer = {"마이클 잭슨", "존 레논", "폴 매카트니", "조지 해리슨"},
        id = 240,
        question = '다음 중 밴드 "비틀스" 멤버가 아닌 사람은?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [241] = {
        answer = {"애드리브", "액션", "프리덤", "칸타레"},
        id = 241,
        question = '"자유롭게"라는 뜻의 라틴어로 대본에 없는 대사를 뜻하는 말은?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [361] = {
        answer = {"무릎", "허리", "배", "턱"},
        id = 361,
        question = '"슬하에 자녀 셋이 있습니다"에서 "슬"이 말하는 부위는?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [362] = {
        answer = {"문어", "오징어", "꼴뚜기", "플라나리아"},
        id = 362,
        question = "무척추동물 중 가장 뇌가 크고 복잡한 동물은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [363] = {
        answer = {"복숭아", "꽃가루", "집먼지", "햇빛"},
        id = 363,
        question = '영화 「기생충」에서 가정도우미 "문숙" 이 가지고 있던 알레르기는?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [364] = {
        answer = {"4", "2", "5", "11"},
        id = 364,
        question = "세종대왕은 조선의 몇 대 왕일까요?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [365] = {
        answer = {"무릎", "허리", "배", "턱"},
        id = 365,
        question = '"슬하에 자녀 셋이 있습니다"에서 "슬"이 말하는 부위는?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [366] = {
        answer = {"위장", "간장", "심장", "신장"},
        id = 366,
        question = '오장육부" 중 "오장"에 해당하지 않는 것은?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [367] = {
        answer = {"폐장", "쓸개", "대장", "소장"},
        id = 367,
        question = '오장육부" 중 "육부"에 해당하지 않는 것은?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [368] = {
        answer = {"45억 년", "35억 년", "58억 년", "60억 년"},
        id = 368,
        question = "현재 과학자들이 추정하는 지구의 대략 나이는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [369] = {
        answer = {"오드리 헵번", "마릴린 먼로", "스칼렛 요한슨", "우마 서먼"},
        id = 369,
        question = "영화 「로마의 휴일」,「티파니에서의 아침을」의 여주인공은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [370] = {
        answer = {"소음", "운동량", "길이", "무게"},
        id = 370,
        question = '"데시벨" 은 무엇을 측정하는 양사일까요?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [371] = {
        answer = {"보조개", "여드름", "몽고반점", "주근깨"},
        id = 371,
        question = '시인 윌리엄 예이츠가 "천사의 실수"라고 표현한 것은?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [372] = {
        answer = {"미국", "아르헨티나", "벨기에", "브라질"},
        id = 372,
        question = "피파(FIFA)랭킹 1위를 해보지 못한 국가는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [373] = {
        answer = {"전기모터", "수소모터", "메탄모터", "태양광모터"},
        id = 373,
        question = "하이브리드카는 가솔린 엔진과 OOOO를 사용합니다.",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [374] = {
        answer = {"진시황", "반고", "여와", "복희"},
        id = 374,
        question = "다음 중 중국 신화에 등장하는 인물이 아닌 것은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [375] = {
        answer = {"진시황", "반고", "여와", "복희"},
        id = 375,
        question = "다음 중 중국 신화에 등장하는 인물이 아닌 것은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [378] = {
        answer = {"미륵사지", "불국사", "첨성대", "천마총"},
        id = 378,
        question = "다음 중 대한민국 경주시에 위치하지 않은 것은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [379] = {
        answer = {"남한산성", "안시성", "삼년산성", "북한산성"},
        id = 379,
        question = "병자호란 때 인조가 청나라 군을 피해 마지막으로 도망간 장소",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [380] = {
        answer = {"화이트칼라", "오렌지칼라", "퍼플칼라", "그레이칼라"},
        id = 380,
        question = "샐러리맨이나 사무직 노동자를 뜻하며 블루칼라와 대비 되는 집단",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [381] = {
        answer = {"진", "조", "위", "한"},
        id = 381,
        question = "중국의 전국 시대를 통일한 나라는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [382] = {
        answer = {"꼼수", "덤", "묘수", "복기"},
        id = 382,
        question = "얕은 속임수로 상대에게 이득을 꾀하는 수를 뜻하는 바둑 용어는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [383] = {
        answer = {"화점", "행마", "포석", "복점"},
        id = 383,
        question = "바둑판 모퉁이에 각 4번째 선이 교차하며 점으로 표시된 곳은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [423] = {
        answer = {"푸바오", "아이바오", "러바오", "크어바오"},
        id = 423,
        question = "최근 한국에서 자연번식에 성공해 에버랜드에서 태어난 판다의 이름은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [424] = {
        answer = {"미술", "머슬", "마술", "모술"},
        id = 424,
        question = '"Art"는 한국말로?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [425] = {
        answer = {"소크라테스", "공자", "장자", "데카르트"},
        id = 425,
        question = '"너 자신을 알라" 라는 말을 남긴 철학자는?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [426] = {
        answer = {"요트", "조정", "카누", "카약"},
        id = 426,
        question = "다음 수상 스포츠 중 노를 사용하지 않는 종목은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [665] = {
        answer = {"EQ", "IQ", "BBQ", "NQ"},
        id = 665,
        question = "감성지수를 뜻하는 말은",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [666] = {
        answer = {"콜럼버스", "마젤란", "구텐베르크", "미켈란젤로"},
        id = 666,
        question = "스페인의 후원을 받아 신대륙 아메리카를 발견한 탐험가의 이름은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [667] = {
        answer = {"알프스", "로키", "히말라야", "우랄"},
        id = 667,
        question = "스위스 이탈리아 프랑스 등 국가에 걸쳐있는 유럽 중남부 산맥은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [668] = {
        answer = {"3D", "3B", "3F", "3A"},
        id = 668,
        question = "더럽고 힘들고 위험한 업종을 흔히 지칭하는 말은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [669] = {
        answer = {"더치커피", "에스프레소", "아포카토", "드립커피"},
        id = 669,
        question = "찬물 또는 상온의 물로 장시간에 걸쳐 추출하는 커피는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [670] = {
        answer = {"르네상스", "보헤미안", "바로크", "로마네스크"},
        id = 670,
        question = '14 ~ 16세기 서유럽에 나타난 "부활, 재생"을 뜻하는 문화운동은?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [671] = {
        answer = {"MSG", "MVP", "MOM", "MLB"},
        id = 671,
        question = "다음 중 화학조미료의 대명사로 불리는 것은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [672] = {
        answer = {"단백질", "지방", "탄수화물", "칼슘"},
        id = 672,
        question = "머리카락과 손톱의 주요 성분은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [673] = {
        answer = {"뉴딜정책", "복지정책", "노동정책", "분배성장대책"},
        id = 673,
        question = "미국의 루스벨트 대통령이 대공황 극복을 위해 추진한 정책은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [674] = {
        answer = {"렛잇고", "렛잇비", "얼음요새", "스노우맨"},
        id = 674,
        question = "애니메이션 「겨울왕국」의 OST는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [675] = {
        answer = {"우사인 볼트", "벤 존슨", "칼루이스", "우사인 와트"},
        id = 675,
        question = "2022년 기준, 남자 육상 100미터 세계 신기록 보유자는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [676] = {
        answer = {"임진왜란", "갑신정변", "을미사변", "병자호란"},
        id = 676,
        question = "명에 가는 길을 내달라는 구실로 일본이 조선에 침입한 전쟁은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [677] = {
        answer = {"상춘재", "춘추관", "관저", "여민관"},
        id = 677,
        question = "청와대 내부 외빈 접견에 주로 사용되는 건물 이름은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [678] = {
        answer = {"크레바스", "크레파스", "크레이지", "크로마뇽"},
        id = 678,
        question = "소득 OOOO은 직장인이 은퇴 후 생기는 소득 공백기를 뜻한다.",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [679] = {
        answer = {"프랑스", "미국", "러시아", "영국"},
        id = 679,
        question = "자유의 여신상을 제작한 나라는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [680] = {
        answer = {"와이어투와이어", "다크호스", "먼치킨", "화이트리스트"},
        id = 680,
        question = "스포츠에서 경기 내내 1등을 차지하여 우승한 것을 이르는 말은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [681] = {
        answer = {"밈주식", "헤지", "공매도", "펀드"},
        id = 681,
        question = "온라인상 입소문으로 투자자의 눈길을 끄는 주식을 이르는 말은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [682] = {
        answer = {"플로깅", "트레킹", "그린 워킹", "플로우"},
        id = 682,
        question = "조깅을 하면서 쓰레기를 줍는 운동은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [683] = {
        answer = {"가스라이팅", "가스테이핑", "가스워싱", "가스라이터"},
        id = 683,
        question = "타인의 심리를 조작하여 그 사람에게 정신적 학대를 하는 행위는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [684] = {
        answer = {"국무총리", "민정수석", "비서실장", "재무차관"},
        id = 684,
        question = "대통령의 명을 받아 행정 각부를 통괄하는 제1위 보좌기관은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [685] = {
        answer = {"DMZ", "DMG", "DNZ", "DNG"},
        id = 685,
        question = "휴전선으로부터 남북으로 각각 2KM의 비무장 지대는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [686] = {
        answer = {"ICBM", "ICBN", "ICVN", "SCBM"},
        id = 686,
        question = "장거리 탄도 미사일을 뜻하는 것은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [687] = {
        answer = {"베르사유조약", "베를린조약", "뮌헨조약", "파리조약"},
        id = 687,
        question = "1919년 연합국과 독일이 맺은 강화조약은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [688] = {
        answer = {"IS", "IOS", "탈레반", "알 카이다"},
        id = 688,
        question = "시리아와 이라크에서 주로 활동하는 이슬람 수니파 무장단체는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [689] = {
        answer = {"히잡", "스카프", "워머", "넥타이"},
        id = 689,
        question = "이슬람 여성들이 머리와 목을 가리기 위해 쓰는 것은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [690] = {
        answer = {"죄와 벌", "안나 카레리나", "바보 이반", "전쟁과 평화"},
        id = 690,
        question = "러시아 작가 톨스토이의 작품이 아닌 것은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [691] = {
        answer = {"카타르시스", "하이라이트", "터닝포인트", "빅뱅"},
        id = 691,
        question = "아리스토텔레스의 『시학』에서 나온 용어로 정화 작용을 뜻하는 단어는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [692] = {
        answer = {"공리주의", "개인주의", "사회주의", "민주주의"},
        id = 692,
        question = "최대 다수의 최대행복을 추구하는 사상은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [693] = {
        answer = {"안전불감증", "안전도피증", "안전결핍증", "안전외면증"},
        id = 693,
        question = "안전에 대한 주의를 느끼지 못하는 증상을 일컫는 말은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [694] = {
        answer = {"상춘재", "춘추관", "관저", "여민관"},
        id = 694,
        question = "청와대 내부 외빈 접견에 주로 사용되는 건물 이름은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [695] = {
        answer = {"그라피티", "그래비티", "그래놀라", "그래미"},
        id = 695,
        question = "건축물의 벽면, 교각 등에 스프레이 페인트로 그림을 그리는 것은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [696] = {
        answer = {"라이온킹", "레미제라블", "오페라의 유령", "캣츠"},
        id = 696,
        question = "세계 4대 뮤지컬에 해당하지 않는 것은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [697] = {
        answer = {"소고", "장구", "징", "북"},
        id = 697,
        question = '사물놀이에 "사물" 로 해당하지 않는 것은?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [698] = {
        answer = {"밥 딜런", "존 레논", "마이클 잭슨", "폴 메카트니"},
        id = 698,
        question = "대중가수 최초로 노벨 문학상을 받은 인물은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [699] = {
        answer = {"커튼콜", "테이블콜", "다이렉트콜", "러브콜"},
        id = 699,
        question = "연극 등의 공연이 끝난 뒤 출연자를 다시 불러내 박수를 보내는 것은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [700] = {
        answer = {"스포일러", "예고편", "쿠키 영상", "엔딩 크레딧"},
        id = 700,
        question = "줄거리나 주요 장면을 미리 밝혀 흥미도를 떨어뜨리는 행위는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [701] = {
        answer = {"퓰리처상", "아카데미상", "노벨상", "안데르센상"},
        id = 701,
        question = "미국에서 가장 권위 있는 보도, 문학, 음악상은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [702] = {
        answer = {"서희", "왕건", "최무선", "일연"},
        id = 702,
        question = "고려시대 문신이자 외교가로 거란을 상대로 강동 6주를 얻은 인물은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [703] = {
        answer = {"동의보감", "명심보감", "본초강목", "민간의서"},
        id = 703,
        question = "1610년 허준이 편찬한 동양 의학 최대의 의학서는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [704] = {
        answer = {"민주", "자유", "박애", "평등"},
        id = 704,
        question = "프랑스 국기의 3가지 색이 상징하는 요소가 아닌 것은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [705] = {
        answer = {"VAR", "VAV", "VRA", "VIP"},
        id = 705,
        question = "축구 경기에서 카메라가 찍은 영상으로 경기 과정을 판독하는 시스템은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [706] = {
        answer = {"해트트릭", "어드벤티지", "오프사이드", "스타플레이"},
        id = 706,
        question = "축구나 하키 등의 경기에서 1명의 마수가 3득점을 올리는 것은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [707] = {
        answer = {"리그 1", "프리미어리그", "프리메라리가", "분데스리가"},
        id = 707,
        question = "유럽축구 4대리그가 아닌 것은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [708] = {
        answer = {"엘 클라시코", "밀라노 더비", "로마 더비", "맨체스터 더비"},
        id = 708,
        question = "레알 마드리드와 FC바르셀로나의 더비 매치를 이르는 말은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [709] = {
        answer = {"홀인원", "멀리건", "이글", "버디"},
        id = 709,
        question = "골프에서 1타로 볼이 그대로 홀에 들어가는 것은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [710] = {
        answer = {"나노기술", "힉스기술", "블록체인", "유전공학"},
        id = 710,
        question = "10억분의 1 수준의 정밀도를 요구하는 극미세가공 과학기술은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [711] = {
        answer = {"비트", "나노", "피코", "마이크로"},
        id = 711,
        question = "다음 중 크기 단위가 아닌 것은",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [712] = {
        answer = {"상대성이론", "만유인력법칙", "양자역학", "엔트로피법칙"},
        id = 712,
        question = "아인슈타인이 수립한 특수 상대성 이론과 일반 상대성 이론의 총칭은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [713] = {
        answer = {"와이파이", "제트파이", "블루투스", "초코파이"},
        id = 713,
        question = "무선접속장치가 설치된 일정 거리 안에서 인터넷이 가능한 통신망은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [714] = {
        answer = {"다크웹", "블루웹", "레드웹", "화이트웹"},
        id = 714,
        question = "일반적인 검색으로 찾을 수 없고 불법적인 정보가 주로 거래되는 웹은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [715] = {
        answer = {"ASMR", "ASNAL", "ASNR", "ASML"},
        id = 715,
        question = "특정 자극을 통해 심리적 안정이나 쾌감을 주는 감각적 경험은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [716] = {
        answer = {"이집트", "대한민국", "그리스", "이탈리아"},
        id = 716,
        question = "다음 중 반도 국가가 아닌 것은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [717] = {
        answer = {"니트족", "스웨터족", "셔츠족", "히피족"},
        id = 717,
        question = "취업, 학업, 가사 등의 일할 의욕이 없는 젋은 층을 일컫는 말은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [718] = {
        answer = {"그루밍족", "털갈이족", "딩크족", "여피족"},
        id = 718,
        question = "패션과 미용 등 외모에 금전, 시간을 투자하는 남성을 일컫는 말은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [719] = {
        answer = {"우리별 1호", "한국별 1호", "세종 1호", "이순신 1호"},
        id = 719,
        question = "우리나라 최초의 소형 과학 실험 위성은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [720] = {
        answer = {"치킨게임", "제로게임", "윈윈", "RPG게임"},
        id = 720,
        question = "한쪽이 양보하지 않을 때 양쪽 모두 파국으로 치닫는 상황은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [721] = {
        answer = {"YOLO", "AOLO", "GOLO", "LOYO"},
        id = 721,
        question = '"당신의 인생은 한 번뿐"이라는 말의 줄임말은?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [722] = {
        answer = {"빅브라더", "빅데이터", "빅파일", "빅파이"},
        id = 722,
        question = "정보독점으로 사회를 통제하는 권력 또는 사회 체계를 일컫는 말은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [723] = {
        answer = {"프로파일러", "프로메테우스", "프로페셔널", "프로이트"},
        id = 723,
        question = "용의자의 성격 행동을 분석하여 도주 경로 등을 추정하는 심리분석관은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [724] = {
        answer = {"소확행", "소확복", "소실행", "소탐대실"},
        id = 724,
        question = '"소소하지만 확실한 행복"의 준말은?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [725] = {
        answer = {"5촌", "4촌", "3촌", "6촌"},
        id = 725,
        question = "당숙은 몇 촌 관계일까요?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [726] = {
        answer = {"GDP", "GOP", "GNP", "GNI"},
        id = 726,
        question = "1년 동안 한 국가에서 생산된 재화와 시장가치를 합산한 것은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [727] = {
        answer = {"우울증질환", "암질환", "뇌혈관질환", "심장질환"},
        id = 727,
        question = "4대 중증질환이 아닌 것은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [728] = {
        answer = {"메르스", "사스", "신종플루", "조류독감"},
        id = 728,
        question = "중동 지역에서 집중적으로 발생한 호흡기 증상을 일으키는 바이러스는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [729] = {
        answer = {"GDP", "GOP", "GNP", "GNI"},
        id = 729,
        question = "1년 동안 한 국가에서 생산된 재화와 시장가치를 합산한 것은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [730] = {
        answer = {"패럴림픽", "올림픽", "월드컵", "아시안게임"},
        id = 730,
        question = "4년 주기로 개최되는 신체장애인들의 국제경기대회는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [731] = {
        answer = {"이스터에그", "쿠키 영상", "웨스턴에그", "트레일러"},
        id = 731,
        question = "게임, 영화에 재미로 몰래 숨겨 놓은 메시지나 기능을 뜻하는 말은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [732] = {
        answer = {"펜데믹", "에피데믹", "엔데믹", "인포데믹"},
        id = 732,
        question = "세계보건기구의 최고 경보 단계는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [733] = {
        answer = {"스페인어", "아랍어", "독일어", "헬라어"},
        id = 733,
        question = "이베리아반도에서 사용되며 중국어 다음으로 많이 사용되는 언어는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [734] = {
        answer = {"열대야", "열대어", "야자수", "백야"},
        id = 734,
        question = "최저기온이 25도 이상인 무더운 밤을 뜻하는 단어는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [735] = {
        answer = {"논개", "유관순", "명성황후", "신사임당"},
        id = 735,
        question = "임진왜란 때 일본군 장수를 유인하여 순국한 여성은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [736] = {
        answer = {"골든 타임", "라스트 타임", "하프 타임", "퍼스트 타임"},
        id = 736,
        question = "생사의 갈림길에 선 환자의 목숨을 구할 수 있는 가장 중요한 시간은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [737] = {
        answer = {"우랄 산맥", "히말라야 산맥", "알프스 산맥", "태백 산맥"},
        id = 737,
        question = "아시아와 유럽의 경계를 형성하는 2000km의 산맥은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [738] = {
        answer = {"철혈정책", "피의정책", "철사정책", "혈열정책"},
        id = 738,
        question = "비스마르크가 독일을 무력 통일하기 위해 펼친 정책은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [739] = {
        answer = {"누리호", "나로호", "아폴로 11호", "텐궁"},
        id = 739,
        question = "국내 기술로 개발한 최초의 저궤도 실용위성 발사용 로켓은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [740] = {
        answer = {"아이언돔", "브론즈돔", "실버돔", "골드돔"},
        id = 740,
        question = "이스라엘에서 개발한 전천후 이동식 방공 시스템은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [741] = {
        answer = {"수에즈 운하", "파나마 운하", "이리 운하", "의항 운하"},
        id = 741,
        question = "지중해와 홍해를 가로지르는 이집트의 운하는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [742] = {
        answer = {"표퓰리즘", "매너리즘", "다다이즘", "커뮤니즘"},
        id = 742,
        question = "정책의 실현성이나 목적을 외면하고 대중의 인기만 영합하는 형태는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [743] = {
        answer = {"중국", "일본", "베트남", "싱가폴"},
        id = 743,
        question = '화폐단위가 "위안" 인 국가는?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [744] = {
        answer = {"태국", "터키", "인도", "말레이시아"},
        id = 744,
        question = '화폐단위가 "밧" 인 국가는?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [745] = {
        answer = {"인도", "중국", "러시아", "몽골"},
        id = 745,
        question = '화폐단위가 "루피" 인 국가는?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [746] = {
        answer = {"쿠알라룸푸르", "방콕", "뉴델리", "하노이"},
        id = 746,
        question = "말레이시아의 수도는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [747] = {
        answer = {"유니코드", "다빈치 코드", "애니그마", "블록체인"},
        id = 747,
        question = "나라별 언어를 모두 표현하기 위해 나온 코드 체계는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [748] = {
        answer = {"대하", "동지", "대설", "입동"},
        id = 748,
        question = "24절기 중 마지막 절기는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [749] = {
        answer = {"소설", "상강", "백로", "처서"},
        id = 749,
        question = '24절기 중 "얼음이 얼기 시작"한다는 뜻을 가진 절기는?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [750] = {
        answer = {"아마존", "알라딘", "타오바오", "교보문고"},
        id = 750,
        question = "전자상거래 기반 세계 최초 인터넷서점은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [751] = {
        answer = {"브렉시트", "엥글로시트", "브렉아웃", "엥글로아웃"},
        id = 751,
        question = "영국의 유럽연합 탈퇴를 의미하는 단어는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [752] = {
        answer = {"풍물놀이", "씨름", "태껸", "강강술래"},
        id = 752,
        question = "인류무형문화유산이 아닌 것은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [753] = {
        answer = {"미장센", "몽타주", "스크립트", "액션"},
        id = 753,
        question = "연극의 무대나 영화의 화면에서 나타내는 시각적 요소를 뜻하는 말은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [754] = {
        answer = {"스낵컬쳐", "쿠키컬쳐", "스낵미디어", "쿠키미디어"},
        id = 754,
        question = "과자처럼 언제 어디서나 간편하게 즐기는 문화 콘텐츠는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [755] = {
        answer = {"PPL", "PVP", "PPT", "POP"},
        id = 755,
        question = "영화, 드라마 등에 자사의 제품을 등장시켜 간접 홍보를 하는 행위는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [756] = {
        answer = {"직지심체요절", "훈민정음", "팔만대장경", "이학지남"},
        id = 756,
        question = "현존하는 세계 최고(最古)의 금속 활자본은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [757] = {
        answer = {"리베로", "스트라이커", "러너", "윙어"},
        id = 757,
        question = "배구와 축구 경기에서 수비 마수를 이르는 말은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [758] = {
        answer = {"은하철도999", "아기공룡 둘리", "뽀로로와친구들", "라바"},
        id = 758,
        question = "다음 중 한국 애니메이션이 아닌 것은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [759] = {
        answer = {"제곱미터", "미터", "기가", "헥타르"},
        id = 759,
        question = "우리나라의 넓이의 법정 계랑 단위는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [760] = {
        answer = {"17", "18", "19", "16"},
        id = 760,
        question = "트와이스 멤버 + HOT 멤버 + 산울림 멤버는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [761] = {
        answer = {"엑스칼리버", "방천화극", "칠지도", "묠니르"},
        id = 761,
        question = "아더 왕 전설 속 아더 왕의 무기 이름은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [762] = {
        answer = {"발할라", "아방궁", "베르사유 궁전", "타지마할"},
        id = 762,
        question = "북유럽 신화에서 오딘을 위해 싸우다가 살해된 전사들이 머무는 궁전은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [763] = {
        answer = {"아틀란티스", "아스가르드", "미드가르드", "요툰하임"},
        id = 763,
        question = "북유럽 신화 세계관의 3개의 나라에 해당하지 않는 나라는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [764] = {
        answer = {"트로이 목마", "네메아의 사자", "히드라", "축사 청소"},
        id = 764,
        question = "그리스 신화 속 헤라클레스의 과업과 관련 없는 것은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [765] = {
        answer = {"세이렌", "메두사", "암사자", "마녀"},
        id = 765,
        question = "그리스 신화 속 등장하는 괴물로 스타벅스의 로고가 된 것은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [766] = {
        answer = {"백설공주", "인어공주", "뮬란", "신데렐라"},
        id = 766,
        question = "월트 디즈니 픽처스에서 개봉한 첫 영화는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [767] = {
        answer = {"명량", "신과함께 1", "극한직업", "왕의 남자"},
        id = 767,
        question = "역대 한국 영화 최다 관객 수를 기록한 영화는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [768] = {
        answer = {"첫사랑", "복수", "비통", "운명"},
        id = 768,
        question = "라일락의 꽃말은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [769] = {
        answer = {"프리지아", "노란 장미", "무궁화", "백합"},
        id = 769,
        question = '"새로운 시작"이라는 꽃말을 가진 꽃은?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [770] = {
        answer = {"아테네", "헤라", "아르테미스", "아프로디테"},
        id = 770,
        question = "그리스 신화에 나오는 신 중 지혜, 전쟁, 등을 관장하는 여신은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [771] = {
        answer = {"오디세우스", "아킬레우스", "아가멤논", "메넬라오스"},
        id = 771,
        question = "트로이 목마를 고안해 낸 그리스 장수는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [772] = {
        answer = {"호메로스", "세익스피어", "존 밀턴", "에우리피데스"},
        id = 772,
        question = "트로이 전쟁 뒤 오디세우스의 귀국 모험담인 『오딧세이』의 작자는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [773] = {
        answer = {"갑골문", "금문", "귀갑문", "은허문"},
        id = 773,
        question = "거북이 등껍질이나 동물 뼈에 새겨진 한자의 직계 조상 문자는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [774] = {
        answer = {"딤섬", "교자", "포자", "쌍화"},
        id = 774,
        question = "삼천 년 전 중국 광둥에서 만들기 시작한, 한입 크기의 만두는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [775] = {
        answer = {"고인돌", "목관묘", "석관묘", "돌방무던"},
        id = 775,
        question = "한국 청동기 시대의 대표적인 무덤 양식, 지석묘라고도 불리는 이것은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [776] = {
        answer = {"모세", "다윗", "솔로몬", "아브라함"},
        id = 776,
        question = "이스라엘의 종교적 지도자이자 영웅으로, 민족 해방을 이룩한 인물은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [777] = {
        answer = {"이토 히로부미", "가토 기요마사", "고니시", "나베시마"},
        id = 777,
        question = "안중근 의사에게 저격당하여 사망한 이 인물은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [778] = {
        answer = {"불혹", "지천명", "약관", "진갑"},
        id = 778,
        question = "나이 40세를 일컫는 말은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [779] = {
        answer = {"이세돌", "이창호", "커제", "린하이펑"},
        id = 779,
        question = "구글의 인공지능 알파고에게 단일 대국 첫 승리를 거둔 인물은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [780] = {
        answer = {"우산국", "우선국", "울성국", "울대국"},
        id = 780,
        question = "울릉도의 옛 이름은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [781] = {
        answer = {"탐라", "탐람", "삼다", "한라"},
        id = 781,
        question = "제주도의 옛 이름은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [782] = {
        answer = {"빙고", "빙관", "빙처", "빙부"},
        id = 782,
        question = '옛 조선시대에서 "얼음"을 관리하던 관아(부서)의 이름은?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [783] = {
        answer = {"운사", "풍사", "화사", "수사"},
        id = 783,
        question = '단군신화에서 "구름"을 관장하는 주술사로 알려진 인물은 누구일까요?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [784] = {
        answer = {"안압지", "관곡지", "혜화지", "서림지"},
        id = 784,
        question = "문무왕 때 축조된 궁원지이며 경주시 인교동에 있는 연못은 무엇일까요?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [785] = {
        answer = {"중국", "일본", "미국", "러시아"},
        id = 785,
        question = "2011년 통계 기준 한국인이 가장 많이 떠난 해외 여행지는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [786] = {
        answer = {"도산서원", "도산공원", "춘추서원", "춘추공원"},
        id = 786,
        question = "퇴계 이황이 세상을 떠난 후 제자들에 의하여 건립된 건축은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [787] = {
        answer = {"네덜란드", "뉴질랜드", "미국", "독일"},
        id = 787,
        question = "2001년 세계 최초로 안락사를 합법화한 나라는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [788] = {
        answer = {"임금피크제", "정년보장제", "임금동결제", "정년동결제"},
        id = 788,
        question = "일정 나이가 되면 임금을 삭감하는 대신 정년을 보장하는 이 제도는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [789] = {
        answer = {"헤밍웨이", "토니 모리슨", "커트 보니것", "피츠제럴드"},
        id = 789,
        question = "노벨문학상을 수상한 미국의 소설가이자, 소설 『노인과 바다』의 저자는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [790] = {
        answer = {"히포크라테스", "소크라테스", "바흐", "화타"},
        id = 790,
        question = "그렇다면 의학의 아버지는 누구일까요?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [791] = {
        answer = {"오랑캐", "간신배", "앞잡이", "돌궐족"},
        id = 791,
        question = "야만스러운 종족이라는 뜻으로, 침략자를 업신여겨 이르던 말은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [792] = {
        answer = {"데자뷰", "랑데뷰", "몽타주", "오션뷰"},
        id = 792,
        question = '"이미 보았다"라는 프랑스어로 처음 본 곳인데 와본 느낌이 드는 현상',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [793] = {
        answer = {"매화", "국화", "재화", "투화"},
        id = 793,
        question = '왕의 얼굴을 "용안" 입을 "구순"이라 했습니다. 그럼 "왕의 변"은?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [794] = {
        answer = {"데칼코마니", "로마네스크", "헤링본", "스케치"},
        id = 794,
        question = "종이 위에 물감을 두껍게 칠하고 반으로 접어 무늬를 만드는 이 기법은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [795] = {
        answer = {"타코야끼", "가쓰오부시", "낫토", "모찌"},
        id = 795,
        question = "밀가루 반죽에 잘게 썬 문어를 넣고 구운 일본 간식은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [796] = {
        answer = {"피타고라스", "아리스토텔레스", "플라톤", "홍성대"},
        id = 796,
        question = "수학의 아버지는 누구일까요?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [797] = {
        answer = {"낭만주의", "체제주의", "현실주의", "허무주의"},
        id = 797,
        question = "18세기 말에서 19세기 중엽까지 유럽과 아메리카에 전파된 예술운동은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [798] = {
        answer = {"해금", "가야금", "거문고", "아쟁"},
        id = 798,
        question = "국악기 중 두 줄로 된 현악기로 깡깡이, 앵금 등으로도 불린 악기는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [799] = {
        answer = {"크샤트리아", "수드라", "바이샤", "브라만"},
        id = 799,
        question = "카스트제도 중 두 번째 계급으로 왕과 무사 계급을 일컫는 호칭은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [800] = {
        answer = {"유토피아", "아틀란티스", "주토피아", "에덴동산"},
        id = 800,
        question = "현실적으로는 아무 데도 존재하지 않는 이상향을 가리키는 꿈의 장소는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [801] = {
        answer = {"하라주쿠", "요코하마", "아사쿠사", "이치방가이"},
        id = 801,
        question = "도쿄 시부야 구에 속해있는 번화가로 젊은이의 거리로 불리는 이곳은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [802] = {
        answer = {"카스트", "골품제", "귀족제", "적서제"},
        id = 802,
        question = "인도에서 직업에 따라 사람들을 네 개의 신분으로 나누는 신분제도는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [803] = {
        answer = {"요실금", "치루", "치질", "요로결석"},
        id = 803,
        question = "본인의 의지와 관계없이 소변이 밖으로 유출되어 속옷을 적시는 현상은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [804] = {
        answer = {"케르베로스", "킬베로스", "히드라", "세이렌"},
        id = 804,
        question = "저승세계의 입구를 지키는 세 개의 머리를 가진 개는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [805] = {
        answer = {"강박증", "우울증", "자폐증", "건망증"},
        id = 805,
        question = "본인의 의지와 무관하게 생각이나 장면이 떠올라 불안해지는 이 질환은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [806] = {
        answer = {"존엄사", "안락사", "사고사", "고독사"},
        id = 806,
        question = "인간으로서 최소한의 품위와 가치를 지키면서 죽을 수 있게 하는 것은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [807] = {
        answer = {"스트리밍", "업로딩", "코딩", "채팅"},
        id = 807,
        question = "인터넷에서 음성이나 영상을 다운로드 없이 실시간으로 재생하는 기법은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [808] = {
        answer = {"피에타", "다비드", "생각하는 사람", "스핑크스"},
        id = 808,
        question = "미켈란젤로가 제작한 성모 마리아가 죽은 그리스도를 안고 있는 동상은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [809] = {
        answer = {"정주영", "정몽주", "정웅인", "정몽준"},
        id = 809,
        question = "현대그룹의 창업자는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [810] = {
        answer = {"입헌군주제", "의원내각제", "대통령제", "군주제"},
        id = 810,
        question = "군주의 권력이 헌법에 의하여 일정한 제약을 받는 정치 체제는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [811] = {
        answer = {"유산슬", "탕수육", "팔보채", "양장피"},
        id = 811,
        question = "육류와 해산물을 썰어서 볶은 후 녹말을 부어 걸쭉하게 만든 중국요리는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [812] = {
        answer = {"푸아그라", "캐비어", "갈레트", "라따뚜이"},
        id = 812,
        question = "크고 지방이 많은 거위 간으로 만든 프랑스 고급 요리인 것은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [813] = {
        answer = {"맷돌춤", "말춤", "셔플", "복고댄스"},
        id = 813,
        question = "2006년, 거북이처럼 목을 뺀 뒤 빙빙 돌리는 이 춤의 이름은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [814] = {
        answer = {"눈썹", "코", "귀", "머리카락"},
        id = 814,
        question = "의학적으로 얼굴과 머리를 구분하는 기준은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [815] = {
        answer = {"심학규", "심전도", "심한길", "심은하"},
        id = 815,
        question = "심청이의 아버지 심봉사의 이름은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [816] = {
        answer = {"사해", "카리브해", "지중해", "오호츠크해"},
        id = 816,
        question = "다음 중 바다가 아닌 곳은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [817] = {
        answer = {"인당수", "정단수", "육각수", "해모수"},
        id = 817,
        question = "심청전에서 심청이가 빠진 곳은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [818] = {
        answer = {"아시아", "아프리카", "유럽", "오세아니아"},
        id = 818,
        question = "인구가 가장 많은 대륙은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [819] = {
        answer = {"토끼", "고슴도치", "뱀", "곰"},
        id = 819,
        question = "겨울잠을 자는 동물이 아닌 것은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [820] = {
        answer = {"지방", "물", "공기", "모래"},
        id = 820,
        question = "낙타의 혹 속에 들어있는 것은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [821] = {
        answer = {"잎", "꽃", "줄기", "가지"},
        id = 821,
        question = "선인장의 가시는 무엇이 변해서 가시가 되었을까요?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [822] = {
        answer = {"목란", "진달래", "국화", "개나리"},
        id = 822,
        question = "북한의 국화는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [823] = {
        answer = {"오리온자리", "궁수자리", "전갈자리", "거문고자리"},
        id = 823,
        question = "다음 중 여름철 별자리가 아닌 것은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [824] = {
        answer = {"무", "마늘", "파", "양파"},
        id = 824,
        question = "다음 중 백합과에 속하는 채소가 아닌 것은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [825] = {
        answer = {"삼계탕", "빈대떡", "신선로", "골동반"},
        id = 825,
        question = "다음 중 음식 이름에 재료의 이름이 들어가는 것은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [826] = {
        answer = {"곱슬머리", "재털이", "덩굴", "걸래"},
        id = 826,
        question = "다음 중 바른 표기법은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [827] = {
        answer = {"화성", "달", "목성", "수성"},
        id = 827,
        question = "오디세이, 글로벌 서베이어, 페스파인더는 무슨 탐사선 이름일까?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [828] = {
        answer = {"수영", "탁구", "양궁", "축구"},
        id = 828,
        question = "다음 종목 중 올림픽 금메달 수가 가장 많은 종목은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [829] = {
        answer = {"어묵", "소시지", "만두", "튀김"},
        id = 829,
        question = '다음 중 북한말 "고기떡"은 무엇을 가리키는 말일까?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [830] = {
        answer = {"장", "떡", "차", "물"},
        id = 830,
        question = "밥은 봄처럼 국은 여름처럼 O은 가을처럼 술은 겨울처럼. 빈칸은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [831] = {
        answer = {"김대중", "김구", "안중근", "윤동주"},
        id = 831,
        question = "한국 최초로 노벨상을 수상한 인물은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [832] = {
        answer = {"김정호", "허준", "김유신", "김춘추"},
        id = 832,
        question = "대동여지도를 만든 인물은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [833] = {
        answer = {"고뿔", "고열", "가람", "곤댓"},
        id = 833,
        question = "감기의 순우리말은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [834] = {
        answer = {"B", "C", "D", "F"},
        id = 834,
        question = "엘리베이터에서 지하층을 표시하는 알파벳은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [835] = {
        answer = {"F", "D", "C", "B"},
        id = 835,
        question = "엘리베이터에서 4층을 표시하는 알파벳은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [836] = {
        answer = {"토끼", "다람쥐", "병아리", "원숭이"},
        id = 836,
        question = '"토사구팽"에서 "토"가 뜻하는 동물은?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [837] = {
        answer = {"해장국", "떡볶이", "짜장면", "설렁탕"},
        id = 837,
        question = "우리나라 최초의 배달 음식은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [838] = {
        answer = {"4", "8", "10", "12"},
        id = 838,
        question = "아리랑 가사에 10리는 몇 킬로미터일까요?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [839] = {
        answer = {"세익스피어", "에미넴", "투팍", "링컨"},
        id = 839,
        question = '힙합에서 자주 쓰이는 "스웨그"라는 표현을 가장 먼저 쓴 인물은?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [840] = {
        answer = {"이스따봉", "씨에씨에", "땡큐", "그라시아스"},
        id = 840,
        question = "다음 외국어 중 감사하다는 표현이 아닌 것은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [841] = {
        answer = {"스승의 날", "어버이 날", "발명의 날", "교정의 날"},
        id = 841,
        question = "세종대왕님의 생일에서 유래 된 기념일은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [842] = {
        answer = {"다보탑", "길성탑", "석가탑", "대보탑"},
        id = 842,
        question = "10원짜리 동전 앞면에 새겨진 탑의 이름은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [843] = {
        answer = {"MP3", "신호등", "전기밥솥", "통조림"},
        id = 843,
        question = "다음 중 우리나라에서 최초로 발명한 것은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [844] = {
        answer = {"와이파이", "이태리 타올", "커피믹스", "PC방"},
        id = 844,
        question = "다음 중 우리나라에서 최초로 발명하지 않은 것은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [845] = {
        answer = {"봉황", "호랑이", "독수리", "용"},
        id = 845,
        question = "희대의 사기꾼 봉이 김선달은 닭을 OO이라고 속였다",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [846] = {
        answer = {"벌레", "새", "개구리", "가축"},
        id = 846,
        question = '"좀이 쑤신다"라는 표현에 "좀"은?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [847] = {
        answer = {"마니아", "마니또", "사이코패스", "히키코모리"},
        id = 847,
        question = '"광기"라는 뜻의 그리스어로 한 가지에 집중하는 사람을 뜻하는 말은?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [848] = {
        answer = {"런칭", "아모르파티", "하쿠나마타타", "아바리티아"},
        id = 848,
        question = '"창을 던지다" 라는 뜻의 라틴어로 현재는 시작을 의미하는 말은?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [849] = {
        answer = {"쥐", "소", "호랑이", "토끼"},
        id = 849,
        question = '십이지신 속 12종의 동물 중 "자"에 해당하는 동물은?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [850] = {
        answer = {"소", "쥐", "호랑이", "토끼"},
        id = 850,
        question = '십이지신 속 12종의 동물 중 "축"에 해당하는 동물은?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [851] = {
        answer = {"호랑이", "쥐", "호랑이", "토끼"},
        id = 851,
        question = '십이지신 속 12종의 동물 중 "인"에 해당하는 동물은?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [852] = {
        answer = {"토끼", "쥐", "개", "용"},
        id = 852,
        question = '십이지신 속 12종의 동물 중 "묘"에 해당하는 동물은?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [853] = {
        answer = {"토끼", "쥐", "개", "용"},
        id = 853,
        question = '십이지신 속 12종의 동물 중 "진"에 해당하는 동물은?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [854] = {
        answer = {"뱀", "뱀", "양", "소"},
        id = 854,
        question = '십이지신 속 12종의 동물 중 "사"에 해당하는 동물은?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [855] = {
        answer = {"말", "용", "호랑이", "토끼"},
        id = 855,
        question = '십이지신 속 12종의 동물 중 "오"에 해당하는 동물은?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [856] = {
        answer = {"양", "양", "소", "닭"},
        id = 856,
        question = '십이지신 속 12종의 동물 중 "미"에 해당하는 동물은?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [857] = {
        answer = {"원숭이", "소", "개", "용"},
        id = 857,
        question = '십이지신 속 12종의 동물 중 "신"에 해당하는 동물은?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [858] = {
        answer = {"닭", "쥐", "개", "용"},
        id = 858,
        question = '십이지신 속 12종의 동물 중 "유"에 해당하는 동물은?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [859] = {
        answer = {"개", "원숭이", "쥐", "호랑이"},
        id = 859,
        question = '십이지신 속 12종의 동물 중 "술"에 해당하는 동물은?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [860] = {
        answer = {"돼지", "용", "뱀", "양"},
        id = 860,
        question = '십이지신 속 12종의 동물 중 "해"에 해당하는 동물은?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [861] = {
        answer = {"스코빌 지수", "다우 지수", "엔도르핀 지수", "코스닥 지수"},
        id = 861,
        question = "매운맛의 강도를 나타내는 지수는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [862] = {
        answer = {"옥자", "괴물", "설국열차", "살인의 추억"},
        id = 862,
        question = "봉준호 감독 작품 중 송강호 배우가 출연하지 않은 작품은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [863] = {
        answer = {"태껸", "우슈", "주짓수", "무에타이"},
        id = 863,
        question = "역대 아시안게임에 한 번도 채택되지 않은 무술은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [864] = {
        answer = {"코로나 블루", "코로나 윈터", "코로나 나이트", "코로나 다운"},
        id = 864,
        question = "코로나 확산으로 사회적 고립감이 커져 생긴 우울감이나 무기력증은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [865] = {
        answer = {"파이어족", "니트족", "캥거루족", "오렌지족"},
        id = 865,
        question = "30대 말이나 40대 초반에 조기 은퇴하겠다는 사람을 지칭하는 말은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [866] = {
        answer = {"개", "원숭이", "쥐", "호랑이"},
        id = 866,
        question = '십이지신 속 12종의 동물 중 "술"에 해당하는 동물은?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [867] = {
        answer = {"일론 머스크", "스티브 잡스", "빌 게이츠", "마윈"},
        id = 867,
        question = "테슬라 모터스, 스페이스X의 CEO는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [868] = {
        answer = {"이상", "김유정", "윤동주", "황순원"},
        id = 868,
        question = "한국식 모더니즘을 대표하는 일제 강점기의 시인이자 작가는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [869] = {
        answer = {"SBS", "KBC", "EBS", "MBC"},
        id = 869,
        question = "다음 중 공영방송 방송국이 아닌 것은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [870] = {
        answer = {"칸타타", "오페라", "오케스트라", "레치타티보"},
        id = 870,
        question = "바로크 시대에 성행했던 가사가 있는 성악곡은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [871] = {
        answer = {"발해", "조선", "고려", "가야"},
        id = 871,
        question = "고구려 유민 대조영이 698년 세운 나라는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [872] = {
        answer = {"10", "100", "50", "500"},
        id = 872,
        question = "유튜브 실버 버튼은 몇만 구독자 이상에게 수여될까요?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [873] = {
        answer = {"십자가", "망치", "창", "말뚝"},
        id = 873,
        question = "다음 중 기독교를 대표하는 상장체는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [874] = {
        answer = {"빨간색", "흰색", "초록색", "회색"},
        id = 874,
        question = "푸시아는 어떤 계열의 색상일까요?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [875] = {
        answer = {"발", "피부", "입", "코"},
        id = 875,
        question = "강아지의 땀샘은 어디에 있습니까?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [876] = {
        answer = {"낙타", "뱀", "생쥐", "투석기"},
        id = 876,
        question = '사막의 "배"로 알려진 동물은?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [877] = {
        answer = {"한복", "코트", "키모노", "셔츠"},
        id = 877,
        question = "한국의 전통의상은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [878] = {
        answer = {"아오자이", "코트", "키모노", "셔츠"},
        id = 878,
        question = "베트남의 전통의상은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [879] = {
        answer = {"스페인", "영국", "미국", "독일"},
        id = 879,
        question = "피카소의 국적은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [880] = {
        answer = {"소화기내과", "소아과", "내과", "산과학"},
        id = 880,
        question = "담도 관련 질환을 담당하는 진료과는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [881] = {
        answer = {"눈썹", "코", "귀", "머리카락"},
        id = 881,
        question = "의학적으로 얼굴과 머리를 구분하는 기준은 어디일까요?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [882] = {
        answer = {"사해", "카리브해", "오호츠크해", "지중해"},
        id = 882,
        question = "다음 중 바다가 아닌 곳은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [883] = {
        answer = {"심학규", "심한길", "심은하", "심전도"},
        id = 883,
        question = "심청이의 아버지 심봉사의 이름은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [884] = {
        answer = {"인당수", "정단수", "육각수", "해모수"},
        id = 884,
        question = "심청전에서 심청이가 빠진 곳은 어디일까요?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [885] = {
        answer = {"노란색", "녹색", "흰색", "파란색"},
        id = 885,
        question = "택시 번호판의 바탕색은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [886] = {
        answer = {"아시아", "아프리카", "아메리카", "유럽"},
        id = 886,
        question = "인구가 가장 많은 대륙은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [887] = {
        answer = {"토끼", "곰", "뱀", "고슴도치"},
        id = 887,
        question = "겨울잠을 자는 동물이 아닌 것은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [888] = {
        answer = {"지방", "물", "공기", "모래"},
        id = 888,
        question = "낙타의 혹 속에는 무엇이 들어있을까요?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [889] = {
        answer = {"뇌", "어금니", "척추", "엉덩이"},
        id = 889,
        question = "골치가 아프다' 중 '골치'란 우리 몸의 어디를 가리킬까요?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [890] = {
        answer = {"경상북도", "강원도", "제주도", "전라남도"},
        id = 890,
        question = "울릉도는 행정구역상 어디에 속할까요?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [891] = {
        answer = {"꺼병이", "꺼실이", "꺼봉이", "꺼벙이"},
        id = 891,
        question = "꿩의 새끼는 뭐라고 할까요?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [892] = {
        answer = {"잎", "가지", "줄기", "꽃"},
        id = 892,
        question = "선인장의 가시는 무엇이 변해서 가시가 되었을까요?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [893] = {
        answer = {"무궁화", "진달래", "개나리", "국화"},
        id = 893,
        question = "우리나라의 국화는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [894] = {
        answer = {"목란", "진달래", "국화", "개나리"},
        id = 894,
        question = "북한의 국화는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [895] = {
        answer = {"오리온자리", "궁수자리", "전갈자리", "거문고자리"},
        id = 895,
        question = "다음 중 여름철 별자리가 아닌것은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [896] = {
        answer = {"울릉도", "강화도", "마라도", "거제도"},
        id = 896,
        question = "도둑,공해,뱀이 없어 삼무도라 불리는 섬은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [897] = {
        answer = {"김유신", "이순신", "장보고", "강감찬"},
        id = 897,
        question = '"충무로"는 누구의 시호를 본 딴 것인가?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [898] = {
        answer = {"중지", "검지", "엄지", "약지"},
        id = 898,
        question = "손가락 중 가장 빨리 자라는 손톱은 무엇인가?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [899] = {
        answer = {"무", "양파", "마늘", "파"},
        id = 899,
        question = "백학과에 속하는 채소가 아닌것은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [900] = {
        answer = {"삼계탕", "빈대떡", "신선로", "골동반"},
        id = 900,
        question = "음식 이름에 재료의 이름이 들어가는 것은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [901] = {
        answer = {"유부녀", "노치녀", "과부", "임산부"},
        id = 901,
        question = '"핫어미"는 무슨 뜻일까요?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [902] = {
        answer = {"혼인신고", "출생신고", "전입신고", "신고식"},
        id = 902,
        question = "법적으로 신고기간이 정해져있지 않은것은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [903] = {
        answer = {"수영", "탁구", "양궁", "봅슬레이"},
        id = 903,
        question = "다음 종목중 올림픽 금메달수가 가장 많은 종목은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [904] = {
        answer = {"베드타운", "신도시", "위성도시", "우보도시"},
        id = 904,
        question = "중심 도시의 주변에서 주거지 역할을 하는 도시를 무엇이라 하는가?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [905] = {
        answer = {"네덜란드", "영국", "미국", "스위스"},
        id = 905,
        question = "세계 최초로 안락사를 합법화한 나라는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [906] = {
        answer = {"님비현상", "임피현상", "님투현상", "핌비현상"},
        id = 906,
        question = '"내 뒷마당에서는 안된다"는 이기주의적 의미로 통용되는 이용어는?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [907] = {
        answer = {"코포라티즘", "컴슈머리즘", "자기조합주의", "경제주의"},
        id = 907,
        question = "자본과 노동에 대한 국가의 통제 방식을 일컫는 말은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [908] = {
        answer = {"야간 근무 금지", "생리휴가", "산후 휴가", "산전휴가"},
        id = 908,
        question = "근로기준법에서 여성과 관련된 내용으로 명시되어 있지 않은 것은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [909] = {
        answer = {"직장폐쇄", "파업", "태업", "생산관리"},
        id = 909,
        question = "다음중 사용자가 취할수 있는 쟁의 행위는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [910] = {
        answer = {"강박관념", "편견", "선입관", "고정관념"},
        id = 910,
        question = "헤일로 효과를 방지하기 위해서 배제해야 할 것이 아닌것은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [911] = {
        answer = {"dB", "ppm", "BOD", "COD"},
        id = 911,
        question = "소음 측정 단위는 어느것인가?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [912] = {
        answer = {"역전층 현상", "온실효과", "황사현상", "스모그 현상"},
        id = 912,
        question = "대기층에서더운 공기 떄문에 그 아래로 찬 공기가 누적되어 나타나는 것은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [913] = {
        answer = {"KT", "품", "GD", "KS"},
        id = 913,
        question = "신기술의 우수한 상품에 매겨지는 마크는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [914] = {
        answer = {"그린라운드", "그린 피스", "녹색운동", "로마클럽"},
        id = 914,
        question = "환경보전을 위한 다자간 협상을 무엇이라 하는가?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [915] = {
        answer = {"칠레", "일본", "멕시코", "브라질"},
        id = 915,
        question = "우리나라의 자유무역 협정 첫 대상국은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [916] = {
        answer = {"시설 임대 산업", "성장 산업", "기간 산업", "여가 산업"},
        id = 916,
        question = "리스 산업이란?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [917] = {
        answer = {"주문자 상표 부착 생산", "하도급", "라이센스 생산", "적기 공급 생산"},
        id = 917,
        question = "OEM 생산 방식이란?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [918] = {
        answer = {"모욕죄", "절도죄", "횡령죄", "재물훼손죄"},
        id = 918,
        question = "친고죄는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [919] = {
        answer = {"시중은행임원", "국회의원", "지방의원", "법관"},
        id = 919,
        question = "다음 중 형법상 수뢰죄의 적용대상이 아닌사람은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [920] = {
        answer = {"방조범", "교사범", "방조범", "공동정범"},
        id = 920,
        question = "공범에 속하지 않는 것은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [921] = {
        answer = {"사기죄", "횡령죄", "배임죄", "절도죄"},
        id = 921,
        question = "다음중 재물죄이며 이익죄인 것은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [922] = {
        answer = {"30일", "40일", "20일", "10일"},
        id = 922,
        question = "여야가 사전에 일정 합의 없이 국회를 열 경우 임시 국회 회기는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [923] = {
        answer = {"1999년", "1997년", "1998년", "2000년"},
        id = 923,
        question = "마카오가 중국으로 반환된 시기는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [924] = {
        answer = {"야당내각", "후보내각", "각외대신", "각내대신"},
        id = 924,
        question = "섀도 캐비닛이란 무엇인가?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [925] = {
        answer = {"어묵", "탕수육", "소세지", "만두"},
        id = 925,
        question = "[북한말] 다음 중 고기떡은 무엇을 가리키는 말일까요?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [926] = {
        answer = {"뮤지컬", "연극", "오페라", "발레"},
        id = 926,
        question = "[북한말] 다음 중 가무이야기는 무엇을 가리키는 말일까요?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [927] = {
        answer = {"젤리", "양갱", "푸딩", "아이스크림"},
        id = 927,
        question = "[북한말] 다음 중 단묵은 무엇을 가리키는 말일까요?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [928] = {
        answer = {"카스테라", "백설기", "찹쌀떡", "식빵"},
        id = 928,
        question = "[북한말] 다음 중 설기과자는 무엇을 가리키는 말일까요?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [929] = {
        answer = {"볶음밥", "강정", "비빔밥", "누룽지"},
        id = 929,
        question = "[북한말] 다음 중 기름밥은 무엇을 가리키는 말일까요?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [930] = {
        answer = {"균깡그리 죽이기", "균사정없이 죽이기", "균퇴치", "균도망가게 하기"},
        id = 930,
        question = "[북한말] 다음 중 멸균울 북한말로 무엇이라 할까요?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [931] = {
        answer = {"라면", "스파케티", "비빔국수", "모밀국수"},
        id = 931,
        question = "[북한말] 다음 중 꼬부랑국수는 무엇을 가리키는 말일까요?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [932] = {
        answer = {"주스", "과일주", "코코넛", "과일통조림"},
        id = 932,
        question = "[북한말] 다음 중 과일단물은 무엇을 가리키는 말일까요?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [933] = {
        answer = {"운동화", "장화", "덧버선", "양말"},
        id = 933,
        question = "[북한말] 다음 중 헝겊신은 무엇을 가리키는 말일까요?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [934] = {
        answer = {"볼펜", "주판", "분필", "계산기"},
        id = 934,
        question = "[북한말] 다음 중 원주필은 무엇을 가리키는 말일까요?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [935] = {
        answer = {"타조", "벌새", "수탉", "독수리"},
        id = 935,
        question = "세계에서 가장 키가 큰 새는 무엇입니까?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [936] = {
        answer = {"식초", "양초", "민초", "화초"},
        id = 936,
        question = "‘초를 친다’라고 할 때 여기서 ‘초’가 의미하는 것은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [937] = {
        answer = {"4년", "3년", "5년", "1년"},
        id = 937,
        question = "우리나라 국회의원의 임기는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [938] = {
        answer = {"솔라닌", "솔라민", "솔라톡", "솔라씩"},
        id = 938,
        question = "감자 싹에 있는 독성 성분의 이름은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [939] = {
        answer = {"인도", "영국", "프랑스", "러시아"},
        id = 939,
        question = "서유기의 삼장법사 일행들이 가는 최종 목적지는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [940] = {
        answer = {"머리카락", "손톱", "발", "머리"},
        id = 940,
        question = "‘간발의 차’에서 ‘간발’이 의미하는 것은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [941] = {
        answer = {"오렌지색", "검은색", "핑크색", "베이지색"},
        id = 941,
        question = "비행기 블랙박스의 색깔은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [942] = {
        answer = {"오매불망", "각주구검", "전전긍긍", "연연불망"},
        id = 942,
        question = '다음 성어 중 "자나 깨나 잊지 못함"을 뜻하는 성어는?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [943] = {
        answer = {"유언비어", "과유불급", "호가호위", "일취월장"},
        id = 943,
        question = '다음 성어 중 "아무 근거 없이 널리 퍼진 소문"을 뜻하는 성어는?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [944] = {
        answer = {"등용문", "금의환향", "입신양명", "유방백세"},
        id = 944,
        question = '다음 성어 중 "입신출세의 관문"을 뜻하는 성어는?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [945] = {
        answer = {"도광양회", "일편단심", "칠전팔기", "파죽지세"},
        id = 945,
        question = '"재능과 명성을 드러내지 않고 참고 기다린다"를 뜻하는 성어는?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [946] = {
        answer = {"장유유서", "부위자강", "일편단심", "군신유의"},
        id = 946,
        question = '"어른과 어린이 사이에는 순서와 질서가 있음"을 뜻하는 성어는?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [947] = {
        answer = {"죽마고우", "관포지교", "간담상조", "문경지교"},
        id = 947,
        question = '"대나무 말을 타고 놀던 가까운 친구"를 뜻하는 성어는?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [948] = {
        answer = {"오동조조", "삼고초려", "도원결의", "괄목상대"},
        id = 948,
        question = "다음 성어 중「삼국지」와 무관한 성어는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [949] = {
        answer = {"아리스토텔레스", "공자", "예수", "석가모니"},
        id = 949,
        question = "다음 중 4대 성인에 속하지 않는 인물은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [950] = {
        answer = {"광해군", "정조", "영조", "태종"},
        id = 950,
        question = "인조반정으로 폐위된 조선의 제16대 왕은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [951] = {
        answer = {"영조", "태종", "세종", "단종"},
        id = 951,
        question = "다음 중 조선 시대에 가장 장수한 왕은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [952] = {
        answer = {"인종", "예종", "정종", "중종"},
        id = 952,
        question = "다음 중 가장 짧은 기간 동안 재위한 조선 왕은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [953] = {
        answer = {"헌종", "순조", "철종", "단종"},
        id = 953,
        question = "다음 중 가장 이른 나이에 왕위에 오른 조선 왕은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [954] = {
        answer = {"강감찬", "이순신", "최영", "곽재우"},
        id = 954,
        question = "다음 인물 중 가장 먼저 태어난 인물은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [955] = {
        answer = {"귀주대첩", "임진왜란", "병자호란", "6.25 전쟁"},
        id = 955,
        question = "다음 사건 중 가장 먼저 발생한 사건은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [956] = {
        answer = {"마동석", "이병헌", "송강호", "최민식"},
        id = 956,
        question = "다음 중 한국인이 아닌 사람은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [957] = {
        answer = {"관상", "겨울왕국 2", "베테랑", "암살"},
        id = 957,
        question = "다음 중 천만 관객 영화가 아닌 것은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [958] = {
        answer = {"의원내각제", "입법", "사법", "행정"},
        id = 958,
        question = "대한민국 삼권분립 체제에 해당하지 않는 것은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [959] = {
        answer = {"하늘", "땅", "저승", "우주"},
        id = 959,
        question = "그리스 신화 속의 제우스는 무엇을 관장하나요?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [960] = {
        answer = {"최적 사용기", "제조일자", "출하일자", "판매 기한"},
        id = 960,
        question = "식품의 유통기한은 무엇을 가리키나?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [961] = {
        answer = {"28일", "100일", "6개월", "3개월"},
        id = 961,
        question = "아기는 생후 며칠까지가 신생아인가?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [962] = {
        answer = {"관성", "만유인력", "가속도", "원심력"},
        id = 962,
        question = "뉴턴 제1법칙은 무엇인가?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [963] = {
        answer = {"바티칸", "리히텐슈타인", "모나코", "수단"},
        id = 963,
        question = "다음 중 가장 작은 나라는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [964] = {
        answer = {"CPU", "GPU", "SDK", "IOI"},
        id = 964,
        question = "컴퓨터의 중앙 처리 장치를 영문으로는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [965] = {
        answer = {"프랑스", "영국", "독일", "미국"},
        id = 965,
        question = "칸 영화제는 어느 나라에서 열리나?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [966] = {
        answer = {"금성", "수성", "지구", "목성"},
        id = 966,
        question = "태양이랑 두 번째 가까운 행성은 무엇인가?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [967] = {
        answer = {"콩비지", "콩나물", "킹콩", "콩자반"},
        id = 967,
        question = "[넌센스] 콩이 바쁘면?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [968] = {
        answer = {"인삼차", "레몬차", "둥굴레차", "고삼차"},
        id = 968,
        question = "[넌센스] 세 사람만 탈 수 있는 차는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [969] = {
        answer = {"서경", "논어", "중용", "대학"},
        id = 969,
        question = "사서삼경의 사서가 아닌 것은 무엇입니까?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [970] = {
        answer = {"마야", "인더스", "이집트", "황하"},
        id = 970,
        question = "세계 4대 문명이 발생한 지역이 아닌 것은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [971] = {
        answer = {"날벼락", "호랑이", "먹구름", "도깨비"},
        id = 971,
        question = "마른하늘에 OOO의 OOO은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [972] = {
        answer = {"포도청", "레몬청", "좌포청", "우포청"},
        id = 972,
        question = "목구멍이 OOO의 OOO은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [973] = {
        answer = {"座", "左", "佐", "坐"},
        id = 973,
        question = '"좌우명"이라는 단어에서 "좌"에 해당하는 한자는?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [974] = {
        answer = {"짜장면", "김밥", "삼겹살", "비빔밥"},
        id = 974,
        question = "4월 14일은 무슨 음식을 먹는 날인가요?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [975] = {
        answer = {"부산", "광주", "인천", "고양"},
        id = 975,
        question = "다음 중 인구가 가장 많은 도시는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [976] = {
        answer = {"금고아", "머리띠", "헤드셋", "밀짚모자"},
        id = 976,
        question = "「서유기」에서 손오공이 머리에 두르고 있는 물건은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [977] = {
        answer = {"5", "1", "3", "12"},
        id = 977,
        question = "가정의 달은 몇 월일까요?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [978] = {
        answer = {"홀인원", "홈런", "덩크", "스핀"},
        id = 978,
        question = "골프에서 티샷이 단번이 홀에 들어가는 것은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [979] = {
        answer = {"아폴로 11호", "나로호", "누리호", "텐궁"},
        id = 979,
        question = "인류 역사 최초로 달에 착륙한 유인우주선은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [980] = {
        answer = {"냉전", "게릴라전", "육탄전", "해물파전"},
        id = 980,
        question = "2차 세계 대전 이후 미국과 소련의 적대 관계를 표현하는 단어는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [981] = {
        answer = {"아프로디테", "헤라", "아테네", "아르테미스"},
        id = 981,
        question = "그리스 신화에서 트로이 왕자 파리스에게 황금 사과를 받은 여신은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [982] = {
        answer = {"이카루스", "헤파이토스", "헤라클레스", "프로메테우스"},
        id = 982,
        question = "그리스 신화 속 다이달로스가 만든 날개를 달고 날다 죽은 사람은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [983] = {
        answer = {"뱀", "호랑이", "독수리", "박쥐"},
        id = 983,
        question = "세계 보건 기구 WHO의 엠블럼에 그려져 있는 동물은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [984] = {
        answer = {"MBTI", "에고그램", "성격유형론", "길퍼드검사"},
        id = 984,
        question = "일일생활에 활용할 수 있도록 고안된 자기 보고식 성격 유형 지표는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [985] = {
        answer = {"마드리드", "바르셀로나", "세비야", "발렌시아"},
        id = 985,
        question = "스페인의 수도는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [986] = {
        answer = {"이산화탄소", "수소", "산소", "규소"},
        id = 986,
        question = "원소기호 CO2는 무엇인가요?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [987] = {
        answer = {"6", "7", "5", "4"},
        id = 987,
        question = "우리나라는 몇 개의 광역시로 이루어져 있나요?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [988] = {
        answer = {"베토벤", "쇼팽", "차이코프스키", "헨델"},
        id = 988,
        question = '피아노곡 "엘리제를 위하여"를 만든 작곡가는?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [989] = {
        answer = {"생텍쥐페리", "톨스토이", "헤밍웨이", "안데르센"},
        id = 989,
        question = "「어린왕자」의 저자는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [990] = {
        answer = {"<코스모스>", "<데미안>", "<변신>", "<반지의 제왕>"},
        id = 990,
        question = "다음 중 천문학자 칼 세이건의 저서는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [991] = {
        answer = {"왕의 남자", "패션왕", "신과함께", "내부자들"},
        id = 991,
        question = "다음 중 웹툰 원작 영화가 아닌 것은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [992] = {
        answer = {"이중섭", "나혜석", "신사임당", "한석봉"},
        id = 992,
        question = "20세기 대한민국 화가로 「싸우는 소」「흰 소」등을 그린 화가는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [993] = {
        answer = {"잭 스패로우", "프로도", "골룸", "레골라스"},
        id = 993,
        question = "「반지의 제왕」 등장인물이 아닌 사람은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [994] = {
        answer = {"미국", "러시아", "호주", "독일"},
        id = 994,
        question = "세계에서 세 번째로 인구가 많은 나라는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [995] = {
        answer = {"미국 국립 박물관", "루브르 박물관", "바티칸 박물관", "영국 박물관"},
        id = 995,
        question = "세계 3대 박물관이 아닌 박물관은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [996] = {
        answer = {"어린 왕자", "연금술사", "빨간 머리 앤", "전쟁과 평화"},
        id = 996,
        question = "다음 중 전 세계 1억 부 이상 판매된 책은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [997] = {
        answer = {"니체", "데카르트", "플라톤", "벤담"},
        id = 997,
        question = '"신은 죽었다"라는 말을 한 철학자는?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [998] = {
        answer = {"상아", "뿔", "어금니", "오돌뼈"},
        id = 998,
        question = "코끼리의 송곳니를 지칭하는 말은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [999] = {
        answer = {"이승만", "안중근", "김구", "최규하"},
        id = 999,
        question = "우리나라 초대 대통령은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [1000] = {
        answer = {"히틀러", "링컨", "장제스", "간디"},
        id = 1000,
        question = "다음 중 1차 세계 대전 참전 인물은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [1001] = {
        answer = {"6", "7", "5", "4"},
        id = 1001,
        question = "우리나라는 몇 개의 광역시로 이루어져 있나요?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [1002] = {
        answer = {"꺼병이", "병아리", "애송이", "고도리"},
        id = 1002,
        question = "꿩의 새끼는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [1003] = {
        answer = {"들국화", "송골매", "샌드페블즈", "무한궤도"},
        id = 1003,
        question = "「매일 그대와」,「행진」을 부른 가수는?「」",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [1004] = {
        answer = {"82", "86", "101", "136"},
        id = 1004,
        question = "한국의 국가 전화번호는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [1005] = {
        answer = {"로미오와줄리엣", "햄릿", "오셀로", "리어왕"},
        id = 1005,
        question = "셰익스피어의 4대 비극이 아닌 것은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [1006] = {
        answer = {"초록", "빨강", "노랑", "파랑"},
        id = 1006,
        question = "색의 3원색이 아닌 것은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [1007] = {
        answer = {"매미", "파리", "모기", "귀뚜라미"},
        id = 1007,
        question = "굼벵이는 어떤 곤충의 애벌레인가요?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [1008] = {
        answer = {"펠레스코어", "메시스코어", "베컴스코어", "지단스코어"},
        id = 1008,
        question = "축구에서 가장 흥미진진한 게임스코어를 이르는 말은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [1009] = {
        answer = {"개골산", "봉래산", "풍악산", "금설산"},
        id = 1009,
        question = "다음 중 금강산의 겨울 이름은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [1010] = {
        answer = {"아르헨티나", "브라질", "칠레", "멕시코"},
        id = 1010,
        question = '춤 "탱고"의 고장은?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [1011] = {
        answer = {"영국", "미국", "홍콩", "호주"},
        id = 1011,
        question = '"BUS" 라는 단어를 처음 사용한 나라는?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [1012] = {
        answer = {"아버지", "어머니", "삼촌", "아들"},
        id = 1012,
        question = '"아바이 순대"에서 "아바이"의 뜻은?',
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [1013] = {
        answer = {"상하이", "하얼빈", "칭다오", "연경"},
        id = 1013,
        question = "다음 도시 중 맥주 브랜드 이름이 아닌 도시는?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [1014] = {
        answer = {"장영실", "박연", "정약용", "정인지"},
        id = 1014,
        question = "자격루를 발명한 사람은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

    [1015] = {
        answer = {"이방원", "정몽주", "이성계", "정종"},
        id = 1015,
        question = "고려시대 시, 하여가(何如歌)를 지은 인물은?",
        random_reward = 1001,
        scene_reward = 1002,
        score_reward = 1002,
    },

}
