-- ./excel/huodong/minglei/dialog.xlsx
return {

    [100] = {
        content = "다과회? 다과회! 다과회! 또 오네,빛나! (초대받은 사람이 도망치려고 하고, 2인 이상이 그를 막을 수 있습니다.)\n진행한 횟수:{done_time}/{totaltime}(오늘{left_buytime}회 구매 가능)",
        dialog_id = 100,
        last_action = {{["content"] = "전투", ["event"] = "MLF"}, {["content"] = "파티 시작", ["event"] = "CT"}, {["content"] = "구매 횟수", ["event"] = "구매"}, {["content"] = "포기"}},
        next = "0",
        pre_id_list = "0",
        status = 2,
        subid = 1,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [200] = {
        content = "귀하가 한 발 늦었습니다,상대는 이미 다른 사람과 전투하고 있습니다,나중에 다시 오십시오.",
        dialog_id = 200,
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 2,
        subid = 1,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [300] = {
        content = "20레벨 미만인 애송이들은 거기에 가지 말아야 합니다.",
        dialog_id = 300,
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 2,
        subid = 1,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [400] = {
        content = "{monstername}과(와) 해보시겠습니까?\n진행 가능 횟수: {left_fighttime}/{totaltime}(오늘{left_buytime}회 매 가능)",
        dialog_id = 400,
        last_action = {{["content"] = "파티 시작", ["event"] = "CT"}, {["content"] = "전투 입장", ["event"] = "나오기"}},
        next = "0",
        pre_id_list = "0",
        status = 2,
        subid = 1,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

}
