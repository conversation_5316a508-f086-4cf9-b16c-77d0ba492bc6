-- ./excel/huodong/block_control.xlsx
return {

    [1001] = {
        FirstCharge = "y",
        LimitReward = "y",
        MonsterAtk = "n",
        OnlineGift = "y",
        TimeLimitRank = "y",
        arenagame = "n",
        chapterfuben = "n",
        description = "야외암뢰（탐색）",
        draw_card = "n",
        endless_pve = "n",
        equalarena = "n",
        equipfuben = "n",
        fieldboss = "n",
        foretell = "y",
        forge = "y",
        grade_gift = "y",
        house = "n",
        id = 1001,
        item = "10020,10021,10022,10024,11822,10019",
        item_resource = "n",
        leitai = "n",
        lilian = "n",
        loginreward = "y",
        map = "n",
        map_click = "n",
        mapbook = "n",
        minglei = "n",
        name = "trapmine",
        org = "y",
        org_activity = "n",
        org_wish = "y",
        orgwar = "y",
        partner = "y",
        partner_resource = "n",
        pata = "n",
        pefuben = "n",
        pk = "n",
        powerguide = "n",
        question = "n",
        rank = "y",
        sceneexam = "n",
        schedule = "n",
        sevendaytarget = "y",
        shop = "y",
        sociality_part = "n",
        store_resource = "n",
        task = "y",
        team = "y",
        teampvp = "n",
        terrawars = "n",
        test_welfare = "n,n",
        tips = "종료 후 시도하세요",
        trapmine = "n",
        travel = "n",
        treasure = "n",
        watchreplay = "n",
        watchwar = "n",
        welfare = "y",
        world_map = "n",
        world_npc = "n",
        worldboss = "n",
    },

    [1002] = {
        FirstCharge = "y",
        LimitReward = "y",
        MonsterAtk = "y",
        OnlineGift = "y",
        TimeLimitRank = "y",
        arenagame = "n",
        chapterfuben = "n",
        description = "호송퀘스트(메인）",
        draw_card = "n",
        endless_pve = "n",
        equalarena = "n",
        equipfuben = "n",
        fieldboss = "n",
        foretell = "y",
        forge = "y",
        grade_gift = "y",
        house = "n",
        id = 1002,
        item = "10020,10021,10022,10024,11822,10019",
        item_resource = "n",
        leitai = "n",
        lilian = "n",
        loginreward = "y",
        map = "n",
        map_click = "n",
        mapbook = "n",
        minglei = "n",
        name = "escort",
        org = "y",
        org_activity = "n",
        org_wish = "y",
        orgwar = "y",
        partner = "y",
        partner_resource = "n",
        pata = "n",
        pefuben = "n",
        pk = "n",
        powerguide = "n",
        question = "n",
        rank = "y",
        sceneexam = "y",
        schedule = "n",
        sevendaytarget = "y",
        shop = "y",
        sociality_part = "n",
        store_resource = "n",
        task = "y",
        team = "n",
        teampvp = "y",
        terrawars = "n",
        test_welfare = "n,n",
        tips = "종료 후 시도하세요",
        trapmine = "n",
        travel = "n",
        treasure = "n",
        watchreplay = "n",
        watchwar = "n",
        welfare = "y",
        world_map = "n",
        world_npc = "n",
        worldboss = "n",
    },

    [1003] = {
        FirstCharge = "y",
        LimitReward = "n,n",
        MonsterAtk = "n,n",
        OnlineGift = "y",
        TimeLimitRank = "y",
        arenagame = "n,n",
        chapterfuben = "n,n",
        description = "고급장비（악목던전）",
        draw_card = "n,n",
        endless_pve = "n",
        equalarena = "n,n",
        equipfuben = "n",
        fieldboss = "n,n",
        foretell = "y",
        forge = "y",
        grade_gift = "y",
        house = "n",
        id = 1003,
        item = "10020,10021,10022,10024,10019,10019",
        item_resource = "n",
        leitai = "n",
        lilian = "n,n",
        loginreward = "n,n",
        map = "y",
        map_click = "y",
        mapbook = "y",
        minglei = "n,n",
        name = "yjfuben",
        org = "y",
        org_activity = "n",
        org_wish = "y",
        orgwar = "y",
        partner = "y",
        partner_resource = "n",
        pata = "n",
        pefuben = "n",
        pk = "n,n",
        powerguide = "n,n",
        question = "y",
        rank = "y",
        sceneexam = "n,n",
        schedule = "n,n",
        sevendaytarget = "y",
        shop = "y",
        sociality_part = "y",
        store_resource = "n",
        task = "n",
        team = "y",
        teampvp = "n,n",
        terrawars = "n,n",
        test_welfare = "n,n",
        tips = "종료 후 시도하세요",
        trapmine = "n",
        travel = "n",
        treasure = "n",
        watchreplay = "n",
        watchwar = "n",
        welfare = "n,n",
        world_map = "n",
        world_npc = "n",
        worldboss = "n,n",
    },

    [1004] = {
        FirstCharge = "n,n",
        LimitReward = "n,n",
        MonsterAtk = "n,n",
        OnlineGift = "n,n",
        TimeLimitRank = "n,n",
        arenagame = "n,n",
        chapterfuben = "n,n",
        description = "야외boss（인형토벌）",
        draw_card = "n",
        endless_pve = "n",
        equalarena = "n,n",
        equipfuben = "n",
        fieldboss = "n,n",
        foretell = "y",
        forge = "n,n",
        grade_gift = "y",
        house = "n",
        id = 1004,
        item = "10020,10021,10022,10024,11822,10019",
        item_resource = "n",
        leitai = "n",
        lilian = "n,n",
        loginreward = "n,n",
        map = "y",
        map_click = "y",
        mapbook = "y",
        minglei = "n",
        name = "fieldboss",
        org = "y",
        org_activity = "n",
        org_wish = "y",
        orgwar = "n",
        partner = "y",
        partner_resource = "n",
        pata = "n",
        pefuben = "n",
        pk = "y",
        powerguide = "n,n",
        question = "n",
        rank = "n,n",
        sceneexam = "n,n",
        schedule = "n,n",
        sevendaytarget = "n,n",
        shop = "y",
        sociality_part = "n",
        store_resource = "n",
        task = "n",
        team = "y",
        teampvp = "n,n",
        terrawars = "n,n",
        test_welfare = "n,n",
        tips = "종료 후 시도하세요",
        trapmine = "n,n",
        travel = "y",
        treasure = "n",
        watchreplay = "n",
        watchwar = "n",
        welfare = "n,n",
        world_map = "n",
        world_npc = "n",
        worldboss = "n,n",
    },

    [1005] = {
        FirstCharge = "n,n",
        LimitReward = "n,n",
        MonsterAtk = "n,n",
        OnlineGift = "n,n",
        TimeLimitRank = "n,n",
        arenagame = "n",
        chapterfuben = "n",
        description = "캐릭터장비（이공유배）",
        draw_card = "n",
        endless_pve = "n",
        equalarena = "n",
        equipfuben = "n",
        fieldboss = "n",
        foretell = "y",
        forge = "n",
        grade_gift = "y",
        house = "n",
        id = 1005,
        item = "10020,10021,10022,10024,10019",
        item_resource = "n",
        leitai = "n",
        lilian = "n",
        loginreward = "y",
        map = "y",
        map_click = "y",
        mapbook = "y",
        minglei = "n",
        name = "equipfuben",
        org = "n",
        org_activity = "n",
        org_wish = "y",
        orgwar = "y",
        partner = "y",
        partner_resource = "n",
        pata = "n",
        pefuben = "n",
        pk = "n",
        powerguide = "y",
        question = "y",
        rank = "n,n",
        sceneexam = "n,n",
        schedule = "n",
        sevendaytarget = "n,n",
        shop = "y",
        sociality_part = "n",
        store_resource = "n",
        task = "n",
        team = "n",
        teampvp = "n,n",
        terrawars = "n",
        test_welfare = "n,n",
        tips = "클리어 혹은 종료 후 시도하세요",
        trapmine = "n",
        travel = "n",
        treasure = "n",
        watchreplay = "n",
        watchwar = "n",
        welfare = "n,n",
        world_map = "n",
        world_npc = "n",
        worldboss = "n",
    },

    [1006] = {
        FirstCharge = "y",
        LimitReward = "y",
        MonsterAtk = "y",
        OnlineGift = "y",
        TimeLimitRank = "y",
        arenagame = "n",
        chapterfuben = "n",
        description = "수행（일일수행）",
        draw_card = "n",
        endless_pve = "n",
        equalarena = "n",
        equipfuben = "n",
        fieldboss = "n",
        foretell = "y",
        forge = "n",
        grade_gift = "y",
        house = "n",
        id = 1006,
        item = "10020,10021,10022,10024,10019",
        item_resource = "n",
        leitai = "n",
        lilian = "y",
        loginreward = "y",
        map = "n",
        map_click = "n",
        mapbook = "y",
        minglei = "n",
        name = "lilian",
        org = "n",
        org_activity = "n",
        org_wish = "y",
        orgwar = "y",
        partner = "y",
        partner_resource = "n",
        pata = "n",
        pefuben = "n",
        pk = "n",
        powerguide = "y",
        question = "y",
        rank = "y",
        sceneexam = "y",
        schedule = "n",
        sevendaytarget = "y",
        shop = "y",
        sociality_part = "n",
        store_resource = "n",
        task = "n",
        team = "n",
        teampvp = "y",
        terrawars = "n",
        test_welfare = "n,n",
        tips = "종료 후 시도하세요",
        trapmine = "n",
        travel = "n",
        treasure = "n",
        watchreplay = "n",
        watchwar = "n",
        welfare = "y",
        world_map = "n",
        world_npc = "n",
        worldboss = "n",
    },

    [1007] = {
        FirstCharge = "n,n",
        LimitReward = "n,n",
        MonsterAtk = "n,n",
        OnlineGift = "n,n",
        TimeLimitRank = "n,n",
        arenagame = "n,n",
        chapterfuben = "n,n",
        description = "일권승부（가위바위보）",
        draw_card = "y",
        endless_pve = "n",
        equalarena = "n,n",
        equipfuben = "n",
        fieldboss = "n,n",
        foretell = "y",
        forge = "y",
        grade_gift = "y",
        house = "n",
        id = 1007,
        item = "10020,10021,10022,10024,11822,10019",
        item_resource = "n",
        leitai = "n",
        lilian = "n,n",
        loginreward = "n,n",
        map = "n",
        map_click = "y",
        mapbook = "n",
        minglei = "n",
        name = "treasure",
        org = "n",
        org_activity = "n",
        org_wish = "y",
        orgwar = "n",
        partner = "y",
        partner_resource = "n",
        pata = "n",
        pefuben = "n",
        pk = "n",
        powerguide = "n,n",
        question = "n",
        rank = "n,n",
        sceneexam = "n,n",
        schedule = "n",
        sevendaytarget = "n,n",
        shop = "y",
        sociality_part = "n",
        store_resource = "n",
        task = "y",
        team = "y",
        teampvp = "n,n",
        terrawars = "n,n",
        test_welfare = "n,n",
        tips = "클리어 혹은 종료 후 시도하세요",
        trapmine = "n,n",
        travel = "n",
        treasure = "n",
        watchreplay = "n",
        watchwar = "n",
        welfare = "n,n",
        world_map = "n",
        world_npc = "y",
        worldboss = "n,n",
    },

    [1008] = {
        FirstCharge = "y",
        LimitReward = "y",
        MonsterAtk = "y",
        OnlineGift = "y",
        TimeLimitRank = "y",
        arenagame = "y",
        chapterfuben = "n",
        description = "파티",
        draw_card = "y",
        endless_pve = "y",
        equalarena = "y",
        equipfuben = "y",
        fieldboss = "y",
        foretell = "y",
        forge = "y",
        grade_gift = "y",
        house = "n",
        id = 1008,
        item = "10024,11822,10019",
        item_resource = "y",
        leitai = "y",
        lilian = "y",
        loginreward = "y",
        map = "y",
        map_click = "y",
        mapbook = "y",
        minglei = "y",
        name = "team",
        org = "y",
        org_activity = "y",
        org_wish = "y",
        orgwar = "y",
        partner = "y",
        partner_resource = "y",
        pata = "n",
        pefuben = "y",
        pk = "y",
        powerguide = "y",
        question = "y",
        rank = "y",
        sceneexam = "y",
        schedule = "y",
        sevendaytarget = "y",
        shop = "y",
        sociality_part = "y",
        store_resource = "y",
        task = "y",
        team = "y",
        teampvp = "y",
        terrawars = "y",
        test_welfare = "n,n",
        tips = "파티탈퇴 후 시도하세요",
        trapmine = "y",
        travel = "y",
        treasure = "n",
        watchreplay = "y",
        watchwar = "y",
        welfare = "y",
        world_map = "y",
        world_npc = "y",
        worldboss = "y",
    },

    [1009] = {
        FirstCharge = "y",
        LimitReward = "y",
        MonsterAtk = "n,n",
        OnlineGift = "y",
        TimeLimitRank = "y",
        arenagame = "n,n",
        chapterfuben = "n,n",
        description = "호송시스템",
        draw_card = "y",
        endless_pve = "n",
        equalarena = "n,n",
        equipfuben = "n",
        fieldboss = "n,n",
        foretell = "y",
        forge = "y",
        grade_gift = "y",
        house = "n",
        id = 1009,
        item = "10020,10021,10022,10024,11822,10019",
        item_resource = "n",
        leitai = "n",
        lilian = "n,n",
        loginreward = "n,n",
        map = "n",
        map_click = "n",
        mapbook = "n",
        minglei = "n",
        name = "convoy",
        org = "n",
        org_activity = "n",
        org_wish = "y",
        orgwar = "n",
        partner = "y",
        partner_resource = "n",
        pata = "n",
        pefuben = "n",
        pk = "n",
        powerguide = "n,n",
        question = "n",
        rank = "y",
        sceneexam = "n,n",
        schedule = "n",
        sevendaytarget = "n,n",
        shop = "y",
        sociality_part = "n",
        store_resource = "n",
        task = "y",
        team = "n",
        teampvp = "n,n",
        terrawars = "n,n",
        test_welfare = "n,n",
        tips = "클리어 혹은 종료 후 시도하세요",
        trapmine = "n,n",
        travel = "n",
        treasure = "n",
        watchreplay = "n",
        watchwar = "n",
        welfare = "y",
        world_map = "n",
        world_npc = "n",
        worldboss = "n,n",
    },

    [1010] = {
        FirstCharge = "n,n",
        LimitReward = "n,n",
        MonsterAtk = "n,n",
        OnlineGift = "n,n",
        TimeLimitRank = "n,n",
        arenagame = "n,n",
        chapterfuben = "n,n",
        description = "협동대련",
        draw_card = "y",
        endless_pve = "n",
        equalarena = "n,n",
        equipfuben = "n",
        fieldboss = "n,n",
        foretell = "y",
        forge = "y",
        grade_gift = "y",
        house = "n",
        id = 1010,
        item = "10020,10021,10022,10024,11822,10019,10030,10040",
        item_resource = "n",
        leitai = "n",
        lilian = "n,n",
        loginreward = "n,n",
        map = "n",
        map_click = "y",
        mapbook = "n",
        minglei = "n",
        name = "teampvp",
        org = "n",
        org_activity = "n",
        org_wish = "y",
        orgwar = "n",
        partner = "y",
        partner_resource = "n",
        pata = "n",
        pefuben = "n",
        pk = "n",
        powerguide = "n,n",
        question = "n",
        rank = "n,n",
        sceneexam = "n,n",
        schedule = "n",
        sevendaytarget = "n,n",
        shop = "y",
        sociality_part = "n",
        store_resource = "n",
        task = "y",
        team = "n,n",
        teampvp = "n,n",
        terrawars = "n,n",
        test_welfare = "n,n",
        tips = "종료 후 시도하세요",
        trapmine = "n,n",
        travel = "n",
        treasure = "n",
        watchreplay = "n",
        watchwar = "n",
        welfare = "n,n",
        world_map = "n,n",
        world_npc = "y",
        worldboss = "n,n",
    },

    [1011] = {
        FirstCharge = "n,n",
        LimitReward = "n,n",
        MonsterAtk = "n,n",
        OnlineGift = "n,n",
        TimeLimitRank = "n,n",
        arenagame = "n,n",
        chapterfuben = "n,n",
        description = "범생어디가",
        draw_card = "n,n",
        endless_pve = "n,n",
        equalarena = "n,n",
        equipfuben = "n,n",
        fieldboss = "n,n",
        foretell = "n,n",
        forge = "n,n",
        grade_gift = "n,n",
        house = "n,n",
        id = 1011,
        item = "",
        item_resource = "n,n",
        leitai = "n,n",
        lilian = "n,n",
        loginreward = "n,n",
        map = "n,n",
        map_click = "n,n",
        mapbook = "n,n",
        minglei = "n,n",
        name = "sceneexam",
        org = "n,n",
        org_activity = "n,n",
        org_wish = "n,n",
        orgwar = "y",
        partner = "n,n",
        partner_resource = "n,n",
        pata = "n,n",
        pefuben = "n,n",
        pk = "n,n",
        powerguide = "n,n",
        question = "y",
        rank = "n,n",
        sceneexam = "y",
        schedule = "n,n",
        sevendaytarget = "n,n",
        shop = "n,n",
        sociality_part = "y",
        store_resource = "n,n",
        task = "n,n",
        team = "n,n",
        teampvp = "n,n",
        terrawars = "n,n",
        test_welfare = "n,n",
        tips = "종료 후 시도하세요",
        trapmine = "n,n",
        travel = "n,n",
        treasure = "n,n",
        watchreplay = "n,n",
        watchwar = "n,n",
        welfare = "n,n",
        world_map = "n,n",
        world_npc = "n,n",
        worldboss = "n,n",
    },

    [1012] = {
        FirstCharge = "n,n",
        LimitReward = "n,n",
        MonsterAtk = "n,n",
        OnlineGift = "n,n",
        TimeLimitRank = "n,n",
        arenagame = "n,n",
        chapterfuben = "n,n",
        description = "세계Boss(봉인된곳)",
        draw_card = "n",
        endless_pve = "n",
        equalarena = "n,n",
        equipfuben = "n",
        fieldboss = "n,n",
        foretell = "y",
        forge = "n,n",
        grade_gift = "n,n",
        house = "n",
        id = 1012,
        item = "10020,10021,10022,10024,11822,10019",
        item_resource = "n",
        leitai = "n",
        lilian = "n,n",
        loginreward = "n,n",
        map = "y",
        map_click = "y",
        mapbook = "y",
        minglei = "n",
        name = "worldboss",
        org = "y",
        org_activity = "n",
        org_wish = "y",
        orgwar = "n,n",
        partner = "y",
        partner_resource = "n",
        pata = "n",
        pefuben = "n",
        pk = "n,n",
        powerguide = "n,n",
        question = "n",
        rank = "n,n",
        sceneexam = "n,n",
        schedule = "n,n",
        sevendaytarget = "n,n",
        shop = "y",
        sociality_part = "n",
        store_resource = "n",
        task = "n",
        team = "n,n",
        teampvp = "n,n",
        terrawars = "n,n",
        test_welfare = "n,n",
        tips = "종료 후 시도하세요",
        trapmine = "n,n",
        travel = "y",
        treasure = "n",
        watchreplay = "n",
        watchwar = "n",
        welfare = "n,n",
        world_map = "n",
        world_npc = "n",
        worldboss = "n,n",
    },

    [1013] = {
        FirstCharge = "n,n",
        LimitReward = "n,n",
        MonsterAtk = "n,n",
        OnlineGift = "n,n",
        TimeLimitRank = "n,n",
        arenagame = "n,n",
        chapterfuben = "n,n",
        description = "길드전",
        draw_card = "y",
        endless_pve = "n",
        equalarena = "n,n",
        equipfuben = "n",
        fieldboss = "n,n",
        foretell = "n,n",
        forge = "y",
        grade_gift = "y",
        house = "n",
        id = 1013,
        item = "10020,10021,10022,10024,10019",
        item_resource = "n",
        leitai = "n",
        lilian = "n,n",
        loginreward = "n,n",
        map = "y",
        map_click = "y",
        mapbook = "n",
        minglei = "n",
        name = "orgwar",
        org = "y",
        org_activity = "n",
        org_wish = "y",
        orgwar = "n,n",
        partner = "y",
        partner_resource = "n",
        pata = "n",
        pefuben = "n",
        pk = "y",
        powerguide = "n,n",
        question = "n",
        rank = "y",
        sceneexam = "n,n",
        schedule = "n,n",
        sevendaytarget = "n,n",
        shop = "y",
        sociality_part = "y",
        store_resource = "n",
        task = "y",
        team = "y",
        teampvp = "n,n",
        terrawars = "n,n",
        test_welfare = "n,n",
        tips = "종료 후 시도하세요",
        trapmine = "n",
        travel = "y",
        treasure = "n",
        watchreplay = "n",
        watchwar = "n",
        welfare = "n,n",
        world_map = "y",
        world_npc = "y",
        worldboss = "n,n",
    },

}
