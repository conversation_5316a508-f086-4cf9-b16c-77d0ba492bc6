-- ./excel/huodong/question/question.xlsx
return {

    [54] = {
        answer = {"대기만성", "대기후"},
        id = 54,
        question = "크고 위대한 사람이 되려면 많은 시간과 노력이 필요하다는 뜻을 가진 말은?",
        reward = 1002,
    },

    [55] = {
        answer = {"마부작침", "마부조침"},
        id = 55,
        question = "도끼를 갈아서 바늘로 만든다 라는 뜻을 담고 있는 좋은 사자성어 인데요 어려운 일도 포기하지 않고 정진하면 이루어 낼수 있다는 뜻으로 사용되는 것은?",
        reward = 1002,
    },

    [56] = {
        answer = {"사필귀정", "사필조정"},
        id = 56,
        question = "어떤 일이라고 하더라도 결국에는 옳은 이치대로 돌아간다는 의미를 가지고 있습니다.옳지 못한것은 길지 못하고 다시 바르게 된다는 이 말은?",
        reward = 1002,
    },

    [57] = {
        answer = {"새응지마", "새응새마"},
        id = 57,
        question = "인생을 살아가다보면 길흉화복.좋은 일과 나쁜 일은 항상 바뀌기 때문에 미리 헤아릴수가 없다는것을 의미하는 단어는?",
        reward = 1002,
    },

    [58] = {
        answer = {"고진감래", "고진감외"},
        id = 58,
        question = "쓴것이 다하면 단것이 온다 라는 의미를 가진것응로 힘든 고생 끝네 즐거움이 온다는 뜻잆니다.",
        reward = 1002,
    },

    [59] = {
        answer = {"온도", "감도"},
        id = 59,
        question = "악어의 알이 부화하는 과정에서 이것에 따라 암컷과 수컷이 결정되는데,여기서 이것은?",
        reward = 1002,
    },

    [77] = {
        answer = {"백령도", "백희도"},
        id = 77,
        question = "우리나라에서 최북단에 있는 섬은?",
        reward = 1002,
    },

    [78] = {
        answer = {"울산광역시", "부산광역시"},
        id = 78,
        question = "우리나라에서 마지막에 광역시가 된 곳은?",
        reward = 1002,
    },

    [79] = {
        answer = {"윌리스 캐리어", "윌리스 휘리어"},
        id = 79,
        question = "에어컨을 발명한 사람은?",
        reward = 1002,
    },

    [80] = {
        answer = {"지방", "피부"},
        id = 80,
        question = "낙타 등에 있는 물질은?",
        reward = 1002,
    },

    [81] = {
        answer = {"차도", "화도"},
        id = 81,
        question = "자전거 도로가 없는 곳에서 자전거는 어디로 다녀야 할까?",
        reward = 1002,
    },

    [82] = {
        answer = {"막 걸러낸 술", "막 걸먹은 술"},
        id = 82,
        question = "막걸리의 뜻은?",
        reward = 1002,
    },

    [83] = {
        answer = {"조지 워싱턴", "죠지 워싱턴"},
        id = 83,
        question = "미국의 초대 대통령은?",
        reward = 1002,
    },

    [84] = {
        answer = {"이승만", "이송만"},
        id = 84,
        question = "우리나라 초대 대동령은?",
        reward = 1002,
    },

    [85] = {
        answer = {"캔버라", "갠버라"},
        id = 85,
        question = "호주의 수도는?",
        reward = 1002,
    },

    [86] = {
        answer = {"베이징", "배이징"},
        id = 86,
        question = "중국의 수도는?",
        reward = 1002,
    },

    [87] = {
        answer = {"5000만원", "7000만원"},
        id = 87,
        question = "예금자 보호법에 의해 각 계좌당 얼마까지 보호가 가능한가?",
        reward = 1002,
    },

    [88] = {
        answer = {"그리스", "크리스"},
        id = 88,
        question = "올림픽 선수단 입장시 항상 먼저 입장하는 나라는?",
        reward = 1002,
    },

    [89] = {
        answer = {"아시아", "아메리카주"},
        id = 89,
        question = "전 세계를 그케 육대주로 나눈다.여기서 가장 큰 대륙은?",
        reward = 1002,
    },

    [90] = {
        answer = {"아마존", "야마존"},
        id = 90,
        question = "세계에서 가장 긴 강의 이름은?",
        reward = 1002,
    },

    [91] = {
        answer = {"혀", "입"},
        id = 91,
        question = "뱀은 어디로 냄새를 맡을까?",
        reward = 1002,
    },

    [92] = {
        answer = {"카트만두", "뉴델리"},
        id = 92,
        question = "네팔의 수도는?",
        reward = 1002,
    },

    [93] = {
        answer = {"분위기 좋은 카페", "분위기 좋은 카플"},
        id = 93,
        question = "신조어 분좋카의 뜻은?",
        reward = 1002,
    },

    [94] = {
        answer = {"귀여워", "사랑해"},
        id = 94,
        question = "700은 무슨 뜻일까요?",
        reward = 1002,
    },

    [95] = {
        answer = {"검은 토끼", "흰색 토끼"},
        id = 95,
        question = "2023년은 계묘년입니다.어떤 동물(띠)의 해 일까요?",
        reward = 1002,
    },

    [96] = {
        answer = {"주황색", "분홍색"},
        id = 96,
        question = "노란색과 빨간색을 섞으면 무슨색이 나올까요?",
        reward = 1002,
    },

    [97] = {
        answer = {"가재", "조개"},
        id = 97,
        question = "ㅇㅇ는 게편 ㅇ에 들어가는 단어는?",
        reward = 1002,
    },

    [98] = {
        answer = {"불국사", "관음사"},
        id = 98,
        question = "다보탑과 석가탑이 있는 경주의 절 이름은?",
        reward = 1002,
    },

    [99] = {
        answer = {"Vote", "Pick"},
        id = 99,
        question = "투표는 영어로 무엇일까요?",
        reward = 1002,
    },

    [100] = {
        answer = {"1080", "720"},
        id = 100,
        question = "다음 해상도(화질)를 나타내는 숫자 중 가장 화질이 좋은 것을 나타내는 것은?",
        reward = 1002,
    },

    [101] = {
        answer = {"바티칸", "괌"},
        id = 101,
        question = "세계에서 가장 작은 나라는?",
        reward = 1002,
    },

    [102] = {
        answer = {"시원찮다", "알고있다"},
        id = 102,
        question = "충청도 사투리 개갈안난다의 뜻은?",
        reward = 1002,
    },

    [103] = {
        answer = {"개미", "송아지"},
        id = 103,
        question = "주식에서 소액을 운용하는 일반 주식투자자를 뭐라고 부를까요?",
        reward = 1002,
    },

    [104] = {
        answer = {"190000", "1"},
        id = 104,
        question = "세상에서 가장 쉬운 숫자는?",
        reward = 1002,
    },

    [105] = {
        answer = {"백태", "치태"},
        id = 105,
        question = "하얗거나 약간 노란색을 띠는 누르스름한 물질이 혓바닥에 끼는것을 무엇이라고 할까요?",
        reward = 1002,
    },

    [106] = {
        answer = {"4월5일", "5월1일"},
        id = 106,
        question = "식목일은 언제일까요?",
        reward = 1002,
    },

    [107] = {
        answer = {"청출어람", "일장춘몽"},
        id = 107,
        question = "제자가 스승보다 더 나음을 비유하는 고사성어는?",
        reward = 1002,
    },

    [108] = {
        answer = {"도롱뇽", "거미"},
        id = 108,
        question = "다음 중 척추동물에 속하는 것은?",
        reward = 1002,
    },

    [109] = {
        answer = {"대학수학능력시험", "수시능력테스트"},
        id = 109,
        question = "수능은 어떤 단어의 줄임말일까요?",
        reward = 1002,
    },

    [110] = {
        answer = {"Ctrl+V", "Ctrl+C"},
        id = 110,
        question = "키보드로 붙여넣기를 하려면 어떤 단축기를 눌러야할까요?",
        reward = 1002,
    },

    [111] = {
        answer = {"쥬뗌므", "곤니찌와"},
        id = 111,
        question = "다음 중 세계 여러나라의 인사말이 아닌것은?",
        reward = 1002,
    },

    [112] = {
        answer = {"교육부", "행정부"},
        id = 112,
        question = "삼권분립에 해당되지 않는 것은?",
        reward = 1002,
    },

    [113] = {
        answer = {"백합", "장미"},
        id = 113,
        question = "순결,변함없는 사랑의 꽃말을 가진 꽃은?",
        reward = 1002,
    },

    [114] = {
        answer = {"화씨온도", "절대온도"},
        id = 114,
        question = "온도를 나타내는 단위에서 C는 섭씨오도라고 읽는다.그럼 F는 뭐라고 읽을까?",
        reward = 1002,
    },

    [115] = {
        answer = {"병아리", "올챙이"},
        id = 115,
        question = "경상도사투리인 삐갱이는 무슨 뜻일까요?",
        reward = 1002,
    },

    [116] = {
        answer = {"러시아", "미국"},
        id = 116,
        question = "백조의 호수를 작곡한 차이코프스키는 어느나라의 작곡가일까요?",
        reward = 1002,
    },

    [117] = {
        answer = {"트라이애슬론", "스키점프"},
        id = 117,
        question = "다음 중 동계올림픽의 종목이 아닌것은?",
        reward = 1002,
    },

    [118] = {
        answer = {"이소연", "지소연"},
        id = 118,
        question = "대한민국 최초의 우주비행사는?",
        reward = 1002,
    },

    [119] = {
        answer = {"조조영화", "심야영화"},
        id = 119,
        question = "영화관에서 이른 오전에 첫 회로 상영하는 영화를 뭐라고 할까요?",
        reward = 1002,
    },

    [120] = {
        answer = {"정보", "호기심"},
        id = 120,
        question = "신조어TMI에서I가 의미하는 것은?",
        reward = 1002,
    },

    [121] = {
        answer = {"일병-상병-병장", "일병-이병-상병"},
        id = 121,
        question = "군대의 계급 순서로 맞는 것은?",
        reward = 1002,
    },

    [122] = {
        answer = {"전촤위복", "고진감래"},
        id = 122,
        question = "화가 바뀌어 오히려 복이 된다라는 뜻의 사자성어는?",
        reward = 1002,
    },

    [123] = {
        answer = {"30km", "50km"},
        id = 123,
        question = "어린이 보호구역에서의 속도제한은 몇km일까요?",
        reward = 1002,
    },

    [124] = {
        answer = {"wet tissue", "water tissue"},
        id = 124,
        question = "물티슈의 영어표기로 알맞는 것은?",
        reward = 1002,
    },

    [149] = {
        answer = {"접영", "평영"},
        id = 149,
        question = "수영의 영법 중 하나로 나비 영법이라고도 불리는 이 영법의 이름은?",
        reward = 1002,
    },

    [150] = {
        answer = {"모공", "각질"},
        id = 150,
        question = "이것은 털이 자라나오는 구멍이라는 뜻으로 피지,여드름과 관련있는 이것은 무엇일까요?",
        reward = 1002,
    },

    [151] = {
        answer = {"국민학교", "국가학교"},
        id = 151,
        question = "초등학교의 옛 명칭은?",
        reward = 1002,
    },

    [152] = {
        answer = {"카이로", "카타르"},
        id = 152,
        question = "이집트의 수도는?",
        reward = 1002,
    },

    [153] = {
        answer = {"대나무 숲", "바닷가"},
        id = 153,
        question = "임금님 귀는 당나귀 귀 라는 동화에서 비밀을 털어놓은 공간은 어디인가요?",
        reward = 1002,
    },

    [154] = {
        answer = {"강화도", "울릉도"},
        id = 154,
        question = "조선시대에 갑비고차라고 불리던 지역은 어디일까요?",
        reward = 1002,
    },

    [155] = {
        answer = {"H", "He"},
        id = 155,
        question = "주가율표에서 첫번째에 있는 수소는 원소기호로는 어떻게  표기할까요?",
        reward = 1002,
    },

    [156] = {
        answer = {"수박", "복숭아"},
        id = 156,
        question = "5천원권 지폐에 있는 과일은?",
        reward = 1002,
    },

    [157] = {
        answer = {"자외선", "적외선"},
        id = 157,
        question = "선크림은 ____차단체 라고도 불린다.빈칸에 들어갈 말은?",
        reward = 1002,
    },

    [158] = {
        answer = {"호랑이", "토끼"},
        id = 158,
        question = "십이간지에서 소의 다음 동물은 무엇일까요?",
        reward = 1002,
    },

    [159] = {
        answer = {"보라색", "주황색"},
        id = 159,
        question = "빨간색과 파랑색을 섞으면 무슨색이 나올까?",
        reward = 1002,
    },

    [160] = {
        answer = {"O형", "B형"},
        id = 160,
        question = "모든 혈액형에게 피를 수혈 해줄수있는 혈액형은 무엇일까요?",
        reward = 1002,
    },

    [161] = {
        answer = {"백조", "사자"},
        id = 161,
        question = "동물 중에 가장 비싼 동물은?",
        reward = 1002,
    },

    [162] = {
        answer = {"응", "흥"},
        id = 162,
        question = "010은 무슨 의미일까요?",
        reward = 1002,
    },

    [163] = {
        answer = {"민주", "인주"},
        id = 163,
        question = "헌법 1조 1항의 빈칸에 들어갈 말은? --대한민국 ㅇㅇ공화국이다.",
        reward = 1002,
    },

    [164] = {
        answer = {"뉴델리", "유델리"},
        id = 164,
        question = "인도의 수도는?",
        reward = 1002,
    },

    [165] = {
        answer = {"스페어", "슈페어"},
        id = 165,
        question = "볼링에서 첫 투구에서 못 쓰러트리고 남은 핀을 처리하는걸 뭐하고 할까요?",
        reward = 1002,
    },

    [166] = {
        answer = {"베토벤", "베도벤"},
        id = 166,
        question = "피아노 곡 엘리제를 위하여를 만든 작곡가는 누구일까요?",
        reward = 1002,
    },

    [167] = {
        answer = {"퇴계 이황", "퇴계 유황"},
        id = 167,
        question = "천원 지폐에 새겨진 인물은 누구일까요?",
        reward = 1002,
    },

    [168] = {
        answer = {"뼈", "피"},
        id = 168,
        question = "게란에도 ㅇ가 있다.ㅇ에 들어갈 말은?",
        reward = 1002,
    },

    [169] = {
        answer = {"러시아", "미국"},
        id = 169,
        question = "세계에서 가장 넓은(큰) 나라는?",
        reward = 1002,
    },

    [170] = {
        answer = {"I DON'T KNOW", "I DON'T KICK"},
        id = 170,
        question = "영어 줄임말에서 IDK는 무슨 뜻일까요?",
        reward = 1002,
    },

    [171] = {
        answer = {"땅콩", "감자"},
        id = 171,
        question = "다음 중 뿌리식물(채소)이 아닌것은?",
        reward = 1002,
    },

    [172] = {
        answer = {";", "!"},
        id = 172,
        question = "세미콜론이라고 불리는 문장 부호는 무엇일까요?",
        reward = 1002,
    },

    [173] = {
        answer = {"(뒤집어서 읽으면)사랑해", "고마워"},
        id = 173,
        question = "H워얼V의 뜻은?",
        reward = 1002,
    },

    [174] = {
        answer = {"루브르박물관", "유브르박물관"},
        id = 174,
        question = "이곳은 프랑스 파리에 위치해 있으며 모나리자가 소장되어 있는데 이곳은 어디 일까요?",
        reward = 1002,
    },

    [175] = {
        answer = {"북극", "남극"},
        id = 175,
        question = "나침반에서 N극이 가리키는 방향은?",
        reward = 1002,
    },

    [176] = {
        answer = {"남극", "북극"},
        id = 176,
        question = "나침반에서 S극이 가리키는 방향은?",
        reward = 1002,
    },

    [177] = {
        answer = {"양파", "마늘"},
        id = 177,
        question = "동글파는 어떤 채소일까요?",
        reward = 1002,
    },

    [178] = {
        answer = {"세종과학기지", "대종과학기지"},
        id = 178,
        question = "대한민국 최초 남극 과학기지로 알려진 이곳의 이름은 무엇일까요?",
        reward = 1002,
    },

    [179] = {
        answer = {"오작교", "오자교"},
        id = 179,
        question = "견우와 직녀는 칠월 칠석에 무엇을 통해 만났나요?",
        reward = 1002,
    },

    [180] = {
        answer = {"아이구", "아이규"},
        id = 180,
        question = "아홉명의 자식을 세글자로 줄이면?",
        reward = 1002,
    },

    [181] = {
        answer = {"11명", "15명"},
        id = 181,
        question = "축구는 몇 명이 경기를 뛸까요?",
        reward = 1002,
    },

    [182] = {
        answer = {"Drive", "Drink"},
        id = 182,
        question = "자동차 기어에서 D는 무엇의 약자일까요?",
        reward = 1002,
    },

    [183] = {
        answer = {"이탈리아", "러시아"},
        id = 183,
        question = "피사의 사탑은 어느 나라에 있을까요?",
        reward = 1002,
    },

    [184] = {
        answer = {"돌아온 싱글", "돌아온 신생아"},
        id = 184,
        question = "돌싱은 무엇의 줄임말일까요?",
        reward = 1002,
    },

    [185] = {
        answer = {"3", "5"},
        id = 185,
        question = "서당 개 ㅇ년에 풍월을 읊는다.ㅇ에 들어가는 숫자는?",
        reward = 1002,
    },

    [186] = {
        answer = {"산소", "이산화탄소"},
        id = 186,
        question = "O2는 무엇을 뜻할까요?",
        reward = 1002,
    },

    [187] = {
        answer = {"이산화탄소", "산소"},
        id = 187,
        question = "CO2는 무엇을 뜻할까요?",
        reward = 1002,
    },

    [188] = {
        answer = {"방콕", "방옥"},
        id = 188,
        question = "태국의 수도는?",
        reward = 1002,
    },

    [189] = {
        answer = {"목화", "묵화"},
        id = 189,
        question = "고려시대에 문익점이 중국에서 이것을 갖고왓다고 알려져있는데 이것은 무엇일까?",
        reward = 1002,
    },

    [190] = {
        answer = {"시다", "달다"},
        id = 190,
        question = "경사도 사투리:키위가 너무쌔끄럽다의 뜻은?",
        reward = 1002,
    },

    [191] = {
        answer = {"박지성", "박유성"},
        id = 191,
        question = "두개의 심장이라는 별명을 가졋던 축구선수는?",
        reward = 1002,
    },

    [192] = {
        answer = {"퍽", "펄"},
        id = 192,
        question = "아이스하키에서 쓰는 공의 이름은?",
        reward = 1002,
    },

    [193] = {
        answer = {"팬데믹", "펀데믹"},
        id = 193,
        question = "요즘같이 전염병이 전 세계적으로 크게 유행하는 현상을 뭐라고 부를까요?",
        reward = 1002,
    },

    [194] = {
        answer = {"100장", "50장"},
        id = 194,
        question = "김을 세는 단위를 톳이라고 합니다.그럼 한톳은 몇장을 의미할까요?",
        reward = 1002,
    },

    [195] = {
        answer = {"일기예보", "주간예보"},
        id = 195,
        question = "131은 어떤 정보를 알려주는 전화일까요?",
        reward = 1002,
    },

    [196] = {
        answer = {"개미", "과자"},
        id = 196,
        question = "다음 중 외래어가 아닌 우리말은?",
        reward = 1002,
    },

    [197] = {
        answer = {"엔", "위안"},
        id = 197,
        question = "우리나라의 화폐단위는 원,일본의 화폐단위는?",
        reward = 1002,
    },

    [205] = {
        answer = {"위안", "엔"},
        id = 205,
        question = "우라나라의 화페단위는 원,중국의 화폐단위는?",
        reward = 1002,
    },

    [206] = {
        answer = {"신석기시대", "구석기시대"},
        id = 206,
        question = "빗살무늬토기는 어느 시대의 유물일까요?",
        reward = 1002,
    },

    [207] = {
        answer = {"1000kg", "2000kg"},
        id = 207,
        question = "1t(톤)을 킬로그램kg으로 나타내면 몇kg일까요?",
        reward = 1002,
    },

    [208] = {
        answer = {"스승의날", "사부의날"},
        id = 208,
        question = "5월 15일은 무슨 기념일 일까요?",
        reward = 1002,
    },

    [209] = {
        answer = {"초록색", "보라색"},
        id = 209,
        question = "노랑색과 파랑색을 섞으면 무슨색이 나올까?",
        reward = 1002,
    },

    [210] = {
        answer = {"시각", "시력"},
        id = 210,
        question = "오감이라 불리는 5가지 감각중 하나 선택하세요.",
        reward = 1002,
    },

    [211] = {
        answer = {"청각", "청력"},
        id = 211,
        question = "오감이라 불리는 5가지 감각중 하나 선택하세요.",
        reward = 1002,
    },

    [212] = {
        answer = {"후각", "후감"},
        id = 212,
        question = "오감이라 불리는 5가지 감각중 하나 선택하세요.",
        reward = 1002,
    },

    [213] = {
        answer = {"미각", "미감"},
        id = 213,
        question = "오감이라 불리는 5가지 감각중 하나 선택하세요.",
        reward = 1002,
    },

    [214] = {
        answer = {"촉각", "총귀"},
        id = 214,
        question = "오감이라 불리는 5가지 감각중 하나 선택하세요.",
        reward = 1002,
    },

    [215] = {
        answer = {"소꿉친구", "친한친구"},
        id = 215,
        question = "북한에서의 송아지친구는 어떤 의미일까요?",
        reward = 1002,
    },

    [216] = {
        answer = {"한의사", "환의사"},
        id = 216,
        question = "손님이 뜸하면 돈 버는 사람은?",
        reward = 1002,
    },

    [217] = {
        answer = {"USB", "USA"},
        id = 217,
        question = "미국에서 비가 내리면?",
        reward = 1002,
    },

    [218] = {
        answer = {"목성", "금성"},
        id = 218,
        question = "태양계에서 가장 큰 행성은?",
        reward = 1002,
    },

    [219] = {
        answer = {"하노이", "하두이"},
        id = 219,
        question = "베트남의 수도는?",
        reward = 1002,
    },

    [220] = {
        answer = {"대기만성", "대기지성"},
        id = 220,
        question = "큰 사람이 되기 위해서는 많은 노력과 시간이 필요하다.큰 그릇은 늦게 이루어진다라는 뜻의 사자성어는?",
        reward = 1002,
    },

    [221] = {
        answer = {"북두칠성", "벽두칠성"},
        id = 221,
        question = "큰곰자라의 꼬리인 7개의 별을 총칭하는 말로 국자모양과 유사한 이 별은 뭇엇인가?",
        reward = 1002,
    },

    [222] = {
        answer = {"1945년", "1955년"},
        id = 222,
        question = "우리나라의 8.15 광복은 몇 년도 일까요?",
        reward = 1002,
    },

    [223] = {
        answer = {"향수병", "상사병"},
        id = 223,
        question = "타지에서 고향(집)을 그리워하는 마음에서 비릇된 이 병의 이름은?",
        reward = 1002,
    },

    [224] = {
        answer = {"사촌", "사돈"},
        id = 224,
        question = "ㅇㅇ이 땅을 사면 배가 아프다.ㅇㅇ에 들어갈 말은?",
        reward = 1002,
    },

    [225] = {
        answer = {"사과나무", "배나무"},
        id = 225,
        question = "쉘 실버스타인의 동화인 아낌없이 주는 나무의 나무는 어떤 나무일까요?",
        reward = 1002,
    },

    [226] = {
        answer = {"제주도", "울릉도"},
        id = 226,
        question = "대한민국에서 가장 큰 섬은?",
        reward = 1002,
    },

    [227] = {
        answer = {"5월", "6월"},
        id = 227,
        question = "가정의 달은 몇 월 일까요?",
        reward = 1002,
    },

    [228] = {
        answer = {"거지", "노숙자"},
        id = 228,
        question = "요즘은 길거리에 걸배이가 거의 없네.걸배이의 뜻은?",
        reward = 1002,
    },

    [229] = {
        answer = {"두꺼비", "개구리"},
        id = 229,
        question = "전래동화 콩쥐팥쥐에서 밑 빠진 독을 막아준 동물은 무엇일까요?",
        reward = 1002,
    },

    [230] = {
        answer = {"검지", "중지"},
        id = 230,
        question = "손가락의 명칭에서 두번째 손가락은?",
        reward = 1002,
    },

    [231] = {
        answer = {"중지", "검지"},
        id = 231,
        question = "손가락의 명칭에서 가운데 손가락은?",
        reward = 1002,
    },

    [232] = {
        answer = {"소지", "약지"},
        id = 232,
        question = "손가락의 명칭에서 새끼 손가락은?",
        reward = 1002,
    },

    [233] = {
        answer = {"sos", "usa"},
        id = 233,
        question = "505는 무슨 의미일까요?",
        reward = 1002,
    },

    [240] = {
        answer = {"상아", "상이"},
        id = 240,
        question = "코끼리의 송곳니를 뭐라고 부를까요?",
        reward = 1002,
    },

    [241] = {
        answer = {"홀인원", "홈런"},
        id = 241,
        question = "골프에서 티샷을 한 공이 단번에 그대로 홀에 들어가는 걸 뭐라고 할까요?",
        reward = 1002,
    },

    [361] = {
        answer = {"1997", "1996"},
        id = 361,
        question = "대한민국의 IMF경제위기는 몇 년도에 발생했을까요?",
        reward = 1002,
    },

    [362] = {
        answer = {"오비이락", "오비추락"},
        id = 362,
        question = "까마귀 날자 배 떨어진다는 뜻의 사자성어는?",
        reward = 1002,
    },

    [363] = {
        answer = {"아야", "어여"},
        id = 363,
        question = "우유가 넘어지면서 하는 말은?",
        reward = 1002,
    },

    [364] = {
        answer = {"조지 워싱턴", "죠지 워싱턴"},
        id = 364,
        question = "1달러에 있느는 인물은 누구일까요?",
        reward = 1002,
    },

    [365] = {
        answer = {"경칩", "중칩"},
        id = 365,
        question = "개구리가 겨울잠에서 깨는 시기라고도 불리며 24절기 중 세번째인 이 절기는 무엇일까요?",
        reward = 1002,
    },

    [366] = {
        answer = {"Function", "Fumction"},
        id = 366,
        question = "키보드에 있는 F1~F12까지 키들의 F의 약자는 무엇일까요?",
        reward = 1002,
    },

    [367] = {
        answer = {"0", "1"},
        id = 367,
        question = "전화기에 있는 모든 숫자를 곱하면?",
        reward = 1002,
    },

    [368] = {
        answer = {"0", "1"},
        id = 368,
        question = "전화기에 있는 모든 숫자를 나두면?",
        reward = 1002,
    },

    [369] = {
        answer = {"냉방병", "냉병"},
        id = 369,
        question = "여름철 냉방과 관련되어 일어나는 가벼운 감기 증세와 비슷한 질환인 이것의 이름은?",
        reward = 1002,
    },

    [370] = {
        answer = {"예루살렘", "에루살렘"},
        id = 370,
        question = "이스라엘의 수도는?",
        reward = 1002,
    },

    [371] = {
        answer = {"영국런던", "프랑스 파리"},
        id = 371,
        question = "본초자오선의 기준인 그리니치춘문대는 어느 나라에 있을까요?",
        reward = 1002,
    },

    [372] = {
        answer = {"눈썹", "눈동자"},
        id = 372,
        question = "초승달은 사람의 ㅇㅇ을 닮아.ㅇㅇ모양의 달이라고도 하는데 ㅇㅇ에 들어갈말은?",
        reward = 1002,
    },

    [373] = {
        answer = {"4개", "5개"},
        id = 373,
        question = "바이올린의 현(줄)은 몇개로 되어있을까요?",
        reward = 1002,
    },

    [374] = {
        answer = {"몽유병", "잠투정"},
        id = 374,
        question = "수면도중 자리에서 일어나 돌아다니는 등 이상행동을 보이는 각성장애인 이것은 무엇일까요?",
        reward = 1002,
    },

    [375] = {
        answer = {"걱정하지 마세요", "손짓하지 마세요"},
        id = 375,
        question = '제주도 사투리"조들지맙서"의 뜻은?',
        reward = 1002,
    },

    [378] = {
        answer = {"얼룩말", "채색말"},
        id = 378,
        question = '북한 사투리 "줄말"이라고 불리는 이 동물은 무엇일까요?',
        reward = 1002,
    },

    [379] = {
        answer = {"100원", "200원"},
        id = 379,
        question = "소주병 빈병(공병)을 팔면 1병당 얼마일까요?",
        reward = 1002,
    },

    [380] = {
        answer = {"떡뽀끼", "떡보기"},
        id = 380,
        question = "남녀노소 좋아하는 간식인 떡볶이를 발음나는대로 적으면?",
        reward = 1002,
    },

    [381] = {
        answer = {"골뱅이", "곱뱅이"},
        id = 381,
        question = "이메일등에 사용하는@부호는 한국에선 뭐라고 부를까요?",
        reward = 1002,
    },

    [382] = {
        answer = {"점심 메뉴 추천해주세요", "점심 메뉴 추리해주세요"},
        id = 382,
        question = "점메추또는 점메추해는 무슨뜻일까요?",
        reward = 1002,
    },

    [383] = {
        answer = {"오타와", "요타와"},
        id = 383,
        question = "캐나다의 수도는?",
        reward = 1002,
    },

    [423] = {
        answer = {"신출귀", "신출누출"},
        id = 423,
        question = "귀신처럼 나타낫다가 귀신처럼 사라진다.사자성어는?",
        reward = 1002,
    },

    [424] = {
        answer = {"8720원", "8770원"},
        id = 424,
        question = "2021년도의 최저시급은 얼마인가?",
        reward = 1002,
    },

    [425] = {
        answer = {"6개", "7개"},
        id = 425,
        question = "대한민국의 광역시는 총 몇개인가?",
        reward = 1002,
    },

    [426] = {
        answer = {"오늘 저녁 치킨 고?", "오늘 저녁 치솔 고?"},
        id = 426,
        question = '"오저치고"의 뜻은?',
        reward = 1002,
    },

    [665] = {
        answer = {"누가 물어본 사람?", "누가 물어본 게임?"},
        id = 665,
        question = "누물보의 뜻은?",
        reward = 1002,
    },

    [666] = {
        answer = {"라이거", "타이어"},
        id = 666,
        question = "수컷사자와 암컷호랑이를 합친 이 동물의 이름은?",
        reward = 1002,
    },

    [667] = {
        answer = {"빨간", "주황"},
        id = 667,
        question = "올림픽의 오륜기 색은?",
        reward = 1002,
    },

    [668] = {
        answer = {"노랑", "보라"},
        id = 668,
        question = "올림픽의 오륜기 색은?",
        reward = 1002,
    },

    [669] = {
        answer = {"초록색", "보라색"},
        id = 669,
        question = "올림픽의 오륜기 색은?",
        reward = 1002,
    },

    [670] = {
        answer = {"파랑", "주황"},
        id = 670,
        question = "올림픽의 오륜기 색은?",
        reward = 1002,
    },

    [671] = {
        answer = {"검정", "화이트"},
        id = 671,
        question = "올림픽의 오륜기 색은?",
        reward = 1002,
    },

    [672] = {
        answer = {"NFC", "NFU"},
        id = 672,
        question = "스마트폰을 기계에 접촉해 카드결제 및 가까운 거ㅣ에서 데이터를 주고받는 이 통신기술은 뭇엇인가?",
        reward = 1002,
    },

    [673] = {
        answer = {"거짓말 하지마", "헛손질 하지마"},
        id = 673,
        question = '사랑의 불시착에 많이 나왓던"후라이까지 말라"는 무슨 뜻일까요?',
        reward = 1002,
    },

    [674] = {
        answer = {"불량식품", "불량식물"},
        id = 674,
        question = "품질이 바람직한 기준보다 떨어지는 식품으로 초등학교 문구방에 자주 파는 이것은?",
        reward = 1002,
    },

    [675] = {
        answer = {"컬레", "걸레"},
        id = 675,
        question = '옷을 세는 단위 한"벌"두"벌".그럼 신발을 세는 단위는?',
        reward = 1002,
    },

    [676] = {
        answer = {"고구마", "감자"},
        id = 676,
        question = '부산 사투리 "고매"는 무슨 뜻일까요?',
        reward = 1002,
    },

    [677] = {
        answer = {"제비", "기러기"},
        id = 677,
        question = "우체국 로고에 있는 새는 무엇일까요?",
        reward = 1002,
    },

    [678] = {
        answer = {"배", "귤"},
        id = 678,
        question = "남의 잔치에 ㅇ놔라 감놔라 한다.ㅇ에 들어갈 말은?",
        reward = 1002,
    },

    [679] = {
        answer = {"감", "귤"},
        id = 679,
        question = "남의 잔치에 배놔라 ㅇ놔라 한다.ㅇ에 들어갈 말은?",
        reward = 1002,
    },

    [680] = {
        answer = {"치과", "학과"},
        id = 680,
        question = "이상한 사람이 가는 곳은?",
        reward = 1002,
    },

    [681] = {
        answer = {"궁시렁", "쿵시렁"},
        id = 681,
        question = "왕이 궁에 들어가기 싫을 때 하는말은?",
        reward = 1002,
    },

    [682] = {
        answer = {"방정환", "박정환"},
        id = 682,
        question = "5월5일 어린이날을 만든 인물은?",
        reward = 1002,
    },

    [683] = {
        answer = {"탄수화물", "수분"},
        id = 683,
        question = "우리 몸에 꼭 필요한 3대 영양소는?",
        reward = 1002,
    },

    [684] = {
        answer = {"지방", "수분"},
        id = 684,
        question = "우리 몸에 꼭 필요한 3대 영양소는?",
        reward = 1002,
    },

    [685] = {
        answer = {"단백질", "수분"},
        id = 685,
        question = "우리 몸에 꼭 필요한 3대 영양소는?",
        reward = 1002,
    },

    [686] = {
        answer = {"여자", "남자"},
        id = 686,
        question = "제주도는 삼다도라고 불리기도 하는데 삼다에 해당되는 것은 무엇일까요?",
        reward = 1002,
    },

    [687] = {
        answer = {"바람", "비"},
        id = 687,
        question = "제주도는 삼다도라고 불리기도 하는데 삼다에 해당되는 것은 무엇일까요?",
        reward = 1002,
    },

    [688] = {
        answer = {"돌", "바위"},
        id = 688,
        question = "제주도는 삼다도라고 불리기도 하는데 삼다에 해당되는 것은 무엇일까요?",
        reward = 1002,
    },

    [689] = {
        answer = {"dB", "dD"},
        id = 689,
        question = "소음 측정단위는 데시벨이라고 하는데 기호로는 어떻게 표시하나요?",
        reward = 1002,
    },

    [690] = {
        answer = {"숭례문", "순례문"},
        id = 690,
        question = "국보1호였던 문화재의 이름은?",
        reward = 1002,
    },

    [691] = {
        answer = {"홍길동전", "홍길문전"},
        id = 691,
        question = "우리나라 최초의 한글 소설로 전해지는 이 고전소설의 이름은?",
        reward = 1002,
    },

    [692] = {
        answer = {"진시황", "전시황"},
        id = 692,
        question = "불로장생을 꿈꿔 전 세계를 뒤져 불로초를 찾았던 왕의 이름은?",
        reward = 1002,
    },

    [693] = {
        answer = {"2018년", "2008년"},
        id = 693,
        question = "평창동계올림픽이 열렸던 해는 몇년도인가요?",
        reward = 1002,
    },

    [694] = {
        answer = {"117", "115"},
        id = 694,
        question = "학교폭력 상담 전화는 몇번일까요?",
        reward = 1002,
    },

    [695] = {
        answer = {"로마", "호마"},
        id = 695,
        question = "이탈리아의 수도는?",
        reward = 1002,
    },

    [696] = {
        answer = {"어부지리", "어부자리"},
        id = 696,
        question = "둘 사이의 다툼에서 제3자가 이득을 본다는 뜻의 사자성어는?",
        reward = 1002,
    },

    [697] = {
        answer = {"아이스크림", "쿠키"},
        id = 697,
        question = '북한 사투리"얼음보숭"이라고 하는것은?',
        reward = 1002,
    },

    [698] = {
        answer = {"잉꼬부부", "애교부부"},
        id = 698,
        question = "다정하고 금실좋은 부부를 비유적으로 나타내는 단어는?",
        reward = 1002,
    },

    [699] = {
        answer = {"모두", "두부"},
        id = 699,
        question = '강원도 사투리 "아줌마 이거 마카 주쇼" 마카는 무슨 뜻일까요?',
        reward = 1002,
    },

    [700] = {
        answer = {"무릎", "다리"},
        id = 700,
        question = '"니 요즘 고뱅이 괜찮나?"고뱅이는 무슨 뜻일까요?',
        reward = 1002,
    },

    [701] = {
        answer = {"electronic", "eleven"},
        id = 701,
        question = "e-mail의 e는 무슨 약자인가?",
        reward = 1002,
    },

    [702] = {
        answer = {"108개", "107개"},
        id = 702,
        question = "야구공의 실밥은 총 몇개일까요?",
        reward = 1002,
    },

    [703] = {
        answer = {"엄청 고생하셨어요", "엄청 노력하셨어요"},
        id = 703,
        question = '제주도"폭삭 속았수다"는 무슨 뜻일까요?',
        reward = 1002,
    },

    [704] = {
        answer = {"언덕", "면담"},
        id = 704,
        question = "오리가 얼면?",
        reward = 1002,
    },

    [705] = {
        answer = {"82", "28"},
        id = 705,
        question = "한국의 국가 전화번호는 몇번일까?",
        reward = 1002,
    },

    [706] = {
        answer = {"가락국수", "바지락국수"},
        id = 706,
        question = "우동의 표준어는 무엇일까요?",
        reward = 1002,
    },

    [707] = {
        answer = {"거문고", "장고"},
        id = 707,
        question = '"심금을 울린다"에서 금은 무엇을 뜻할까요?',
        reward = 1002,
    },

    [708] = {
        answer = {"곰의 새끼", "사자 새끼"},
        id = 708,
        question = [["능소니'는 무엇을 뜻할까요?]],
        reward = 1002,
    },

    [709] = {
        answer = {"일백 백", "하얀 백"},
        id = 709,
        question = "백합꽃의 백은 어떤 한자를 쓸까요?",
        reward = 1002,
    },

    [710] = {
        answer = {"가을추", "추운추"},
        id = 710,
        question = '"추호도 없다"에서 추는 어떤 한자를 쓸까요?',
        reward = 1002,
    },

    [711] = {
        answer = {"마름질", "기름질"},
        id = 711,
        question = "옷감이나 재목을 치수에 맞춰 자르는 일을 순우리말로 무엇이라 할까요?",
        reward = 1002,
    },

    [712] = {
        answer = {"불가항력", "전대미문"},
        id = 712,
        question = "사람의 힘으로는 저항할수 없는힘을 뜻하는 말은?",
        reward = 1002,
    },

    [713] = {
        answer = {"전대미문", "불가항력"},
        id = 713,
        question = "이제까지 한번도 들어본적 없다 라는뜻을 가진 단어는?",
        reward = 1002,
    },

    [714] = {
        answer = {"누란지위", "쾌도난마"},
        id = 714,
        question = "매우 아슬아슬하면서 위태로운 형세를 나타내는 말은?",
        reward = 1002,
    },

    [715] = {
        answer = {"쾌도난마", "누란지위"},
        id = 715,
        question = "복잡하게 뒤얽혀있는 사물이나 일을 강력한 힘으로 명쾌하게 처리한다는 뜻을 가진 사자성어는?",
        reward = 1002,
    },

    [716] = {
        answer = {"도청도설", "누란지위"},
        id = 716,
        question = "길거리에 퍼져서 널리 알려지고 있는 뜬소문을 말하는 것은?",
        reward = 1002,
    },

    [717] = {
        answer = {"유명무실", "불가항력"},
        id = 717,
        question = "이름은 그럴듯 하고 있어보이는데 정작 실속이 없을때 쓰는 사자성어는?",
        reward = 1002,
    },

    [718] = {
        answer = {"견리사의", "불가항력"},
        id = 718,
        question = "눈앞의 이익을 보게 되면 의리를 먼저 생각하라는 뜻을 가진 말은?",
        reward = 1002,
    },

    [719] = {
        answer = {"광해군", "공해군"},
        id = 719,
        question = "인조반정으로 폐위된 조선의 제 16대 왕은?",
        reward = 1002,
    },

    [720] = {
        answer = {"서서", "누워서"},
        id = 720,
        question = "말은 잠을 잘 때 어떤 자세로 잠을 잘까?",
        reward = 1002,
    },

    [721] = {
        answer = {"허균", "허군"},
        id = 721,
        question = "홍길동 전의 저자는?",
        reward = 1002,
    },

    [722] = {
        answer = {"머리카락", "손톱"},
        id = 722,
        question = "간발의 차'의 뜻은 이것만큼 아주 작은 차이라고 말한다. 이것은 무엇?",
        reward = 1002,
    },

    [723] = {
        answer = {"고도리", "고구마"},
        id = 723,
        question = "고등어의 새끼를 부르는 명칭은?",
        reward = 1002,
    },

    [724] = {
        answer = {"고막", "피지"},
        id = 724,
        question = "귀청 떨어지다'의 귀청의 뜻은?",
        reward = 1002,
    },

    [725] = {
        answer = {"얼레", "열레"},
        id = 725,
        question = "연날리기를 할 때, 연줄을 감았다 풀었다 하는 기구는 무엇?",
        reward = 1002,
    },

    [726] = {
        answer = {"고구려", "일본"},
        id = 726,
        question = "온달과 평강공주는 어느 나라 사람인가?",
        reward = 1002,
    },

    [727] = {
        answer = {"김정호", "김장호"},
        id = 727,
        question = "대동여지도를 만든 사람은?",
        reward = 1002,
    },

    [728] = {
        answer = {"허밍웨이", "하밍웨이"},
        id = 728,
        question = "노인과 바다'의 작가는?",
        reward = 1002,
    },

    [729] = {
        answer = {"워싱턴 D.C", "오사카"},
        id = 729,
        question = "미국의 수도는?",
        reward = 1002,
    },

    [730] = {
        answer = {"장영실", "장연실"},
        id = 730,
        question = "조선시대 자격루를 발명한 사람은?",
        reward = 1002,
    },

    [731] = {
        answer = {"3.3제곱미터", "3.2제곱미터"},
        id = 731,
        question = "부동산 1평의 면적은?",
        reward = 1002,
    },

    [732] = {
        answer = {"4번째", "5번째"},
        id = 732,
        question = "세종대왕은 조선시대 몇 번째 왕?",
        reward = 1002,
    },

    [733] = {
        answer = {"핀란드", "런던"},
        id = 733,
        question = "산타가 태어난 국가는?",
        reward = 1002,
    },

    [734] = {
        answer = {"인당수", "약당수"},
        id = 734,
        question = "심청이가 공양미 삼백석에 빠진 곳은?",
        reward = 1002,
    },

    [735] = {
        answer = {"1년", "2년"},
        id = 735,
        question = "지구가 태양 주위 한바퀴 도는데 걸리는 기간?",
        reward = 1002,
    },

    [736] = {
        answer = {"나비효과", "제비효과"},
        id = 736,
        question = "어느 한곳에서 시작된 나비 날갯짓이 뉴욕에 태풍을 일으킨다는 이론",
        reward = 1002,
    },

    [737] = {
        answer = {"필리핀", "말레시아"},
        id = 737,
        question = "아시아에서 최초로 민주주의 국가가 된 곳?",
        reward = 1002,
    },

    [738] = {
        answer = {"노란색", "빨간색"},
        id = 738,
        question = "양궁 표적의 10점 배경 중간 색상은?",
        reward = 1002,
    },

    [739] = {
        answer = {"대장간", "대장금"},
        id = 739,
        question = "쓸데없는 행동을 뜻하는 부질없다 유래장소?",
        reward = 1002,
    },

    [740] = {
        answer = {"051", "052"},
        id = 740,
        question = "부산의 지역번호는?",
        reward = 1002,
    },

    [741] = {
        answer = {"남극", "북극"},
        id = 741,
        question = "남극과 북극 중 더 추운 곳은?",
        reward = 1002,
    },

    [742] = {
        answer = {"금성", "목성"},
        id = 742,
        question = "지구에서 가장 가까운 우주행성?",
        reward = 1002,
    },

    [743] = {
        answer = {"1440분", "1450분"},
        id = 743,
        question = "하루 24시간은 몇분인가?",
        reward = 1002,
    },

    [744] = {
        answer = {"배드민턴", "야구"},
        id = 744,
        question = "스포츠 중 경기공이 가장 빠른 스포츠는?",
        reward = 1002,
    },

    [745] = {
        answer = {"경상북도", "경상남도"},
        id = 745,
        question = "울릉도의 행정구역은?",
        reward = 1002,
    },

    [746] = {
        answer = {"아시아", "유럽"},
        id = 746,
        question = "인구가 가장 많은 대륙은?",
        reward = 1002,
    },

    [747] = {
        answer = {"프랑스", "영국"},
        id = 747,
        question = "피자는 어느나라 음식일까?",
        reward = 1002,
    },

    [748] = {
        answer = {"압록강", "한강"},
        id = 748,
        question = "우리나라에서 가장 긴 강?",
        reward = 1002,
    },

    [749] = {
        answer = {"러시아", "미국"},
        id = 749,
        question = "세계에서 가장 큰 나라는?",
        reward = 1002,
    },

    [750] = {
        answer = {"돼지", "사슴"},
        id = 750,
        question = "윷놀이에서 도를 뜻하는 동물은?",
        reward = 1002,
    },

    [751] = {
        answer = {"왼손", "오른손"},
        id = 751,
        question = "설날에 세배시 남자는 어느손이 위로 올라갈까?",
        reward = 1002,
    },

    [752] = {
        answer = {"세명대학교", "서울대학교"},
        id = 752,
        question = "한국에서 가장 들어가기 어려운 대학?",
        reward = 1002,
    },

    [753] = {
        answer = {"호주머니", "세러머니"},
        id = 753,
        question = "호주의 돈은?",
        reward = 1002,
    },

    [754] = {
        answer = {"칠레", "북한"},
        id = 754,
        question = "조폭이 가장 많은 나라?",
        reward = 1002,
    },

    [755] = {
        answer = {"창피해", "쪽팔려"},
        id = 755,
        question = "창으로 찌를때 하는 말?",
        reward = 1002,
    },

    [756] = {
        answer = {"인도", "일본"},
        id = 756,
        question = "차도가 없는 국가?",
        reward = 1002,
    },

    [757] = {
        answer = {"이번주 비빔밥", "오늘 비빔밥"},
        id = 757,
        question = "전주 비빔밥 반댓말?",
        reward = 1002,
    },

    [758] = {
        answer = {"불경기", "불구경"},
        id = 758,
        question = "자영업자들이 싫어하는 말?",
        reward = 1002,
    },

    [759] = {
        answer = {"킹콩", "깅쿵"},
        id = 759,
        question = "왕이 넘어지면?",
        reward = 1002,
    },

    [760] = {
        answer = {"중고차", "세차장"},
        id = 760,
        question = "중학생과 고등학생이 타는 자동차?",
        reward = 1002,
    },

    [761] = {
        answer = {"누네띠네", "누네안뗘"},
        id = 761,
        question = "도둑이 제일 싫어하는 과자는?",
        reward = 1002,
    },

    [762] = {
        answer = {"바이킹", "굿바이"},
        id = 762,
        question = "신하가 임금이랑 헤어질 때 하는 인사?",
        reward = 1002,
    },

    [763] = {
        answer = {"모짜렐라", "모텔리어"},
        id = 763,
        question = "신데렐라가 잠을 못자면?",
        reward = 1002,
    },

    [764] = {
        answer = {"맥시코", "돼지코"},
        id = 764,
        question = "세상에서 가장 큰 코?",
        reward = 1002,
    },

    [765] = {
        answer = {"최저임금", "최소임금"},
        id = 765,
        question = "세상에서 가장 돈이 없는 임금?",
        reward = 1002,
    },

    [766] = {
        answer = {"철들때", "먹을때"},
        id = 766,
        question = "인간의 몸무게가 가장 많이 나갈때는?",
        reward = 1002,
    },

    [767] = {
        answer = {"파인애플", "바나나"},
        id = 767,
        question = "사과를 한입 먹으면?",
        reward = 1002,
    },

    [768] = {
        answer = {"풋사과", "청사과"},
        id = 768,
        question = "사과가 웃으면?",
        reward = 1002,
    },

    [769] = {
        answer = {"삐약", "배약"},
        id = 769,
        question = "병아리가 먹는 약은?",
        reward = 1002,
    },

    [770] = {
        answer = {"이별", "기별"},
        id = 770,
        question = "별 중에서 가장 슬픈 별",
        reward = 1002,
    },

    [771] = {
        answer = {"선물", "눈물"},
        id = 771,
        question = "물 중에서 가장 좋은 물",
        reward = 1002,
    },

    [772] = {
        answer = {"나무", "화분"},
        id = 772,
        question = "무가 자기 소개할떄 하는 말",
        reward = 1002,
    },

    [773] = {
        answer = {"고집", "빈집"},
        id = 773,
        question = "목수가 고칠 수 없는 집",
        reward = 1002,
    },

    [774] = {
        answer = {"2등", "1등"},
        id = 774,
        question = "달리기 시합에서 2등을 체지면 몇등?",
        reward = 1002,
    },

    [775] = {
        answer = {"도장", "고장"},
        id = 775,
        question = "누를수록 더욱 선명해지는 것?",
        reward = 1002,
    },

    [776] = {
        answer = {"치과의사", "수의사"},
        id = 776,
        question = "매일 이상한 것만 보는 사람?",
        reward = 1002,
    },

    [777] = {
        answer = {"이쑤시개", "부대찌개"},
        id = 777,
        question = "고기를 먹을때 따라오는 개는?",
        reward = 1002,
    },

    [778] = {
        answer = {"에그머니", "아주머니"},
        id = 778,
        question = "계란을 팔고 받는 돈은?",
        reward = 1002,
    },

    [779] = {
        answer = {"나이", "국수"},
        id = 779,
        question = "일년에 1번만 먹을 수 있는 것?",
        reward = 1002,
    },

    [780] = {
        answer = {"고등어", "다시마"},
        id = 780,
        question = "물고기 중에서 가장 공부를 잘하는 물고기는?",
        reward = 1002,
    },

    [781] = {
        answer = {"지원봉사자", "저승사자"},
        id = 781,
        question = "세상에서 가장 착한 사자?",
        reward = 1002,
    },

    [782] = {
        answer = {"경제", "문화"},
        id = 782,
        question = "인간의 생활에 필요한 재화나 용역을 생산ㆍ분배ㆍ소비하는 모든 활동. 또는 그것을 통하여 이루어지는 사회적 관계.",
        reward = 1002,
    },

    [783] = {
        answer = {"자본", "경제"},
        id = 783,
        question = "재화와 용역의 생산에 사용되는 자산",
        reward = 1002,
    },

    [784] = {
        answer = {"자산", "자본"},
        id = 784,
        question = "개인이나 법인이 소유하고 있는 유형 ·무형의 유가치물",
        reward = 1002,
    },

    [785] = {
        answer = {"부채", "부동산"},
        id = 785,
        question = "재화(財貨)나 용역(用役)의 차입(借入)을 전제로 부담한 금전상의 상환의무",
        reward = 1002,
    },

    [786] = {
        answer = {"채무", "재무"},
        id = 786,
        question = "채권관계에서 어떤 급부를 이행해야만 하는 의무",
        reward = 1002,
    },

    [787] = {
        answer = {"종합소득세", "종합소유권"},
        id = 787,
        question = "모든 소득을 종합하여 과세하는 조세",
        reward = 1002,
    },

    [788] = {
        answer = {"소득공제", "소득공유"},
        id = 788,
        question = "소득을 대상으로 하는 조세의 과세표준을 계산하기 위하여 소득액에서 법정 금액을 공제하는 것",
        reward = 1002,
    },

    [789] = {
        answer = {"연말정산", "연말결제"},
        id = 789,
        question = "급여(給與)소득에서 원천징수한 세액(稅額)의 과부족을 연말에 정산하는 일",
        reward = 1002,
    },

    [790] = {
        answer = {"다다익선", "다다선행"},
        id = 790,
        question = "많으면 많을수록 더 좋다는 뜻. 多 : 많을 다多 : 많을 다益 : 더할 익善 : 좋을 선",
        reward = 1002,
    },

    [791] = {
        answer = {"결초보은", "결초보답"},
        id = 791,
        question = "풀을 묶어 은혜를 갚음. 즉 죽어서도 잊지 않고 은혜를 갚음",
        reward = 1002,
    },

    [792] = {
        answer = {"동문서답", "동문서문"},
        id = 792,
        question = "동쪽을 묻자 서쪽을 답한다. 질문에 대해 엉뚱한 대답을 늘어놓는 것.",
        reward = 1002,
    },

    [793] = {
        answer = {"배은망덕", "배은방득"},
        id = 793,
        question = "은혜를 배신하고 베풀어 준 덕을 잊음",
        reward = 1002,
    },

    [794] = {
        answer = {"동고동락", "동고통보"},
        id = 794,
        question = "고생도 함께 하고 기쁨도 함께 함",
        reward = 1002,
    },

    [795] = {
        answer = {"동병상련", "동고통보"},
        id = 795,
        question = "같은 병을 앓아 서로를 가엾게 여기다",
        reward = 1002,
    },

    [796] = {
        answer = {"유유상종", "우유부단"},
        id = 796,
        question = "같은 동아리끼리 서로 왕래하여 사귄다는 뜻으로, 비슷한 부류의 인간 모임을 비유한 말",
        reward = 1002,
    },

    [797] = {
        answer = {"새옹지마", "새옹시마"},
        id = 797,
        question = "새옹의 말. 즉 변방 노인의 말처럼 복이 화가 되기도 하고, 화가 복이 될 수도 있음. 인생의 앞을 내다볼 수 없음을 의미하기도 함.",
        reward = 1002,
    },

    [798] = {
        answer = {"전화위복", "전화위화"},
        id = 798,
        question = "재앙이 복으로 바뀜. 화가 복이 될 수도 있고, 복이 화가 될 수도 있다는 순환하는 세상 이치를 가리키는 말.",
        reward = 1002,
    },

    [799] = {
        answer = {"사필귀정", "사필조정"},
        id = 799,
        question = "무슨 일이든 결국 옳은 이치대로 돌아간다는 뜻",
        reward = 1002,
    },

    [800] = {
        answer = {"피그말리온 효과", "파그말리온 효과"},
        id = 800,
        question = "긍정적인 기대나 관심이 사람에게 좋은 영향을 미치는 효과",
        reward = 1002,
    },

    [801] = {
        answer = {"피터팬 효과", "피마팬 효과"},
        id = 801,
        question = "성인이 되어서도 현실을 도피하기 위해 스스로를 어른임을 인정하지 않은 채 타인에게 의존하고 싶어 하는 심리",
        reward = 1002,
    },

    [802] = {
        answer = {"벽에 붙은 파리 효과", "벽에 붙은 거미 효과"},
        id = 802,
        question = "어떤 일에 실패하거나 좌절했을 때, 제3자의 입장으로 자신의 상황을 객관적으로 바라보면 긍정적인 결과가 나타나는 현상",
        reward = 1002,
    },

    [803] = {
        answer = {"넛지효과", "대지효과"},
        id = 803,
        question = "넛지(nudge)는 '옆구리를 슬쩍 찌른다.'는 뜻으로 강요에 의하지 않고 유연하게 개입함으로써 선택을 유도하는 방법",
        reward = 1002,
    },

    [804] = {
        answer = {"파파게노 효과", "파포게노 효과"},
        id = 804,
        question = "자살과 관련한 언론보도를 자제하고, 신중한 보도를 함으로써 자살을 예방할 수 있는 효과",
        reward = 1002,
    },

    [805] = {
        answer = {"자이가르닉 효과", "자이가르 효과"},
        id = 805,
        question = "마치지 못한 일을 마음속에서 쉽게 지우지 못하는 현상",
        reward = 1002,
    },

    [806] = {
        answer = {"크레스피 효과", "크레스파 효과"},
        id = 806,
        question = "보상과 벌점 강도가 점점 강해져야 일의 수행 능률이 계속해서 증가할 수 있다는 효과",
        reward = 1002,
    },

    [807] = {
        answer = {"베르테르 효과", "베르다르 효과"},
        id = 807,
        question = "유명인 또는 평소 존경하거나 선망하던 인물이 자살할 경우, 그 인물과 자신을 동일시해서 자살을 시도하는 현상",
        reward = 1002,
    },

    [808] = {
        answer = {"밴드왜건 효과", "밴드요견 효과"},
        id = 808,
        question = "대중적으로 유행하는 정보를 따라 상품을 구매하는 현상",
        reward = 1002,
    },

    [809] = {
        answer = {"플라시보 효과", "플라시요 효과"},
        id = 809,
        question = "의사가 효과 없는 가짜 약 혹은 꾸며낸 치료법을 환자에게 제안했는데, 환자의 긍정적인 믿음으로 인해 병세가 호전되는 현상",
        reward = 1002,
    },

    [810] = {
        answer = {"스티그마 효과", "스티크마 효과"},
        id = 810,
        question = "부정적으로 낙인찍히면 실제로 그 대상이 점점 더 나쁜 행태를 보이고, 또한 대상에 대한 부정적 인식이 지속되는 현상",
        reward = 1002,
    },

    [811] = {
        answer = {"에펠탑 효과", "에펠드 효과"},
        id = 811,
        question = "처음에는 싫어하거나 무관심했지만 대상에 대한 반복노출이 거듭될수록 호감도가 증가하는 현상",
        reward = 1002,
    },

    [812] = {
        answer = {"효손 효과", "효자 효과"},
        id = 812,
        question = "다른 사람의 시선을 인식할 때, 본래 의도나 천성과 다르게 행동하는 현상",
        reward = 1002,
    },

    [813] = {
        answer = {"쿨리지 효과", "쿨르지 효과"},
        id = 813,
        question = "성관계를 맺는 파트너를 바꾸었을 때 성(性)적 욕망이 증가하는 현상",
        reward = 1002,
    },

    [814] = {
        answer = {"스트룹 효과", "스트루 효과"},
        id = 814,
        question = "단어를 인지하는 과정에서 그 단어의 의미와 글자의 색상이 일치하지 않은 조건(예 : 빨간색으로 쓰인 ‘검정’이라는 글자)에서 색상을 명명하는 반응속도가 늦어지는 현상",
        reward = 1002,
    },

    [815] = {
        answer = {"파노플리 효과", "파노플라 효과"},
        id = 815,
        question = "특정 상품을 사며 동일 상품 소비자로 예상되는 집단과 자신을 동일시하는 현상",
        reward = 1002,
    },

    [816] = {
        answer = {"맥거핀 효과", "맥거판 효과"},
        id = 816,
        question = "영화에서, 중요한 것처럼 등장하지만 실제로는 줄거리와 전혀 상관없이 관객들의 주의를 분산시키기 위해 사용하는 극적 장치 혹은 속임수",
        reward = 1002,
    },

    [817] = {
        answer = {"노시보 효과", "노시노 효과"},
        id = 817,
        question = [=[약을 올바로 처방했는데도 환자가 의심을 품으면 약효가 나타나지 않는 현상
화]=],
        reward = 1002,
    },

    [818] = {
        answer = {"후광 효과", "후상 효과"},
        id = 818,
        question = "한 대상의 두드러진 특성이 그 대상의 다른 세부 특성을 평가하는 데에도 영향을 미치는 현상",
        reward = 1002,
    },

    [819] = {
        answer = {"부메랑 효과", "부마랑 효과"},
        id = 819,
        question = "어떤 행위가 행위자의 의도를 벗어나 불리한 결과로 되돌아오는 것",
        reward = 1002,
    },

    [820] = {
        answer = {"최신 효과", "최대 효과"},
        id = 820,
        question = "가장 나중에 혹은 최근에 제시된 정보를 더 잘 기억하는 현상",
        reward = 1002,
    },

    [821] = {
        answer = {"베블렌 효과", "베블린 효과"},
        id = 821,
        question = "가격이 오르는 데도 불구하고 수요가 증가하는 효과",
        reward = 1002,
    },

    [822] = {
        answer = {"스놉 효과", "스돕 효과"},
        id = 822,
        question = "어떤 상품에 대한 사람들의 소비가 증가하면 오히려 그 상품의 수요가 줄어드는 효과",
        reward = 1002,
    },

    [823] = {
        answer = {"디드로 효과", "디드라 효과"},
        id = 823,
        question = "하나의 물건을 구입한 후 그 물건과 어울리는 다른 제품들을 계속 구매하는 현상",
        reward = 1002,
    },

    [824] = {
        answer = {"방관자 효과", "방치자 효과"},
        id = 824,
        question = "주변에 사람이 많으면 많을수록 책임이 분산되어 오히려 위험에 처한 사람을 덜 돕게 되는 현상",
        reward = 1002,
    },

    [825] = {
        answer = {"웨스터마크 효과", "웨스터마 효과"},
        id = 825,
        question = "유아기부터 오랜 기간 함께 자란 남녀는 서로를 너무 잘 알고 있기에 서로에게 성(性)적 매력을 느끼지 못한다는 이론",
        reward = 1002,
    },

    [826] = {
        answer = {"점화 효과", "점요 효과"},
        id = 826,
        question = "먼저 제시된 점화 단어(Priming word)에 의해 나중에 제시된 표적 단어(Target word)를 해석하는 데 영향을 받는 현상",
        reward = 1002,
    },

    [827] = {
        answer = {"플린 효과", "플란 효과"},
        id = 827,
        question = "시간이 지날수록 세대들의 IQ(Intelligence Quotient, 지능 지수) 검사 평균 성적이 계속 높아지는 현상",
        reward = 1002,
    },

    [828] = {
        answer = {"가르시아 효과", "가르시마 효과"},
        id = 828,
        question = "어떤 음식을 먹은 후 구토나 복통 같은 불쾌함을 경험할 경우 다음부터 그 음식을 먹지 않게 되는 현상",
        reward = 1002,
    },

    [829] = {
        answer = {"칵테일파티 효과", "칵테르파티 효과"},
        id = 829,
        question = "칵테일파티처럼 여러 사람의 목소리와 잡음이 많은 상황에서도 본인이 흥미를 갖는 이야기는 선택적으로 들을 수 있는 현상",
        reward = 1002,
    },

    [830] = {
        answer = {"한국도로공사", "기상청"},
        id = 830,
        question = "폭설로 고속도로에 갇힌 사람들의 일부가 소송을한다. 소송대상은?",
        reward = 1002,
    },

    [831] = {
        answer = {"입크기", "방귀소리크기"},
        id = 831,
        question = "수컷 하마끼리 싸움에서 우열을 가리는것은?",
        reward = 1002,
    },

    [832] = {
        answer = {"도시이름", "사람이름"},
        id = 832,
        question = "온천을 뜻하는 스파의 유래는?",
        reward = 1002,
    },

    [833] = {
        answer = {"10*26사건", "10*2사건"},
        id = 833,
        question = "1979년 10월 26일 저녁 7시 40분경 서울 종로구 궁정동 중앙정보부 안가(安家)에서 중앙정보부 부장 김재규(金載圭)가 대통령 박정희를 살해한 사건",
        reward = 1002,
    },

    [834] = {
        answer = {"6월 항쟁", "7월 항쟁"},
        id = 834,
        question = "1979년 12·12사태로 정권을 잡은 전두환 군사정권의 장기집권을 저지하기 위해 일어난 범국민적 민주화운동",
        reward = 1002,
    },

    [835] = {
        answer = {"5*18광주민주화운동", "5*8광주민주화운동"},
        id = 835,
        question = "1980년 5월 18일부터 27일까지 광주광역시(당시 광주시)와 전라남도 지역의 시민들이 벌인 민주화 운동",
        reward = 1002,
    },

    [836] = {
        answer = {"박종철 고문치사사건", "박성철 고문치사사건"},
        id = 836,
        question = "1987년 1월 14일 서울대 학생이었던 박종철 군이 서울 남영동 치안본부 대공분실에서 조사 받던 중 고문으로 숨진 사건",
        reward = 1002,
    },

    [837] = {
        answer = {"4*13호헌조치", "4*3호헌조치"},
        id = 837,
        question = "1987년 4월 13일 제5공화국 대통령 전두환(全斗煥)이 국민들의 민주화 요구를 거부하고, 일체의 개헌 논의를 중단시킨 조치",
        reward = 1002,
    },

    [838] = {
        answer = {"이한열사망사건", "이한영사망사건"},
        id = 838,
        question = "1987년 6월 9일, 연세대학교 정문 앞에서 1천 여 명의 학생이 대 정부 시위를 벌이던 중 이 학교의 경영학과 2학년 이한열이 경찰이 쏜 최루탄에 맞아 사망한 사건",
        reward = 1002,
    },

    [839] = {
        answer = {"홍고추", "청고추"},
        id = 839,
        question = "비타민 A의 함량이 더 많은 고추는?",
        reward = 1002,
    },

    [840] = {
        answer = {"갑신정변", "갑산정변"},
        id = 840,
        question = "1884년(고종 21) 김옥균, 박영효, 홍영식, 서광범, 서재필 등 급진개화파가 청나라로부터의 독립과 조선의 개화를 목표로 일으킨 정변.",
        reward = 1002,
    },

    [841] = {
        answer = {"임오군란", "임군란"},
        id = 841,
        question = "1882년(고종 19) 6월 9일 구식군대가 일으킨 병란",
        reward = 1002,
    },

    [842] = {
        answer = {"동학운동", "동문운동"},
        id = 842,
        question = "1894년(고종 31) 전라도 고부의 동학접주 전봉준(全琫準) 등을 지도자로 동학교도와 농민들이 합세하여 일으킨 농민운동",
        reward = 1002,
    },

    [843] = {
        answer = {"갑오개혁", "갑을개혁"},
        id = 843,
        question = "1894년(고종 31) 7월부터 1896년 2월까지 추진되었던 일련의 개혁운동",
        reward = 1002,
    },

    [844] = {
        answer = {"을미사변", "을마사변"},
        id = 844,
        question = "1895년(고종 32) 일본공사 미우라 고로[三浦梧樓]가 주동이 되어 명성황후(明成皇后)를 시해하고 일본세력 강화를 획책한 정변",
        reward = 1002,
    },

    [845] = {
        answer = {"을사조약", "을소조약"},
        id = 845,
        question = "1905년 일본이 한국의 외교권을 박탈하기 위해 강제로 체결한 조약",
        reward = 1002,
    },

    [846] = {
        answer = {"러일전쟁", "로일전쟁"},
        id = 846,
        question = "1904∼1905년에 만주와 한국의 지배권을 두고 러시아와 일본이 벌인 전쟁",
        reward = 1002,
    },

    [847] = {
        answer = {"청일전쟁", "천일전쟁"},
        id = 847,
        question = "1894~1895년 조선의 지배를 둘러싸고 중국(청)과 일본 간에 벌어진 전쟁",
        reward = 1002,
    },

    [848] = {
        answer = {"시모노세키조약", "시모도세키조약"},
        id = 848,
        question = "청 ·일전쟁의 전후처리를 위해 1895년 4월 17일 청국과 일본이 일본 시모노세키에서 체결한 강화조약",
        reward = 1002,
    },

    [849] = {
        answer = {"아관파천", "야관파천"},
        id = 849,
        question = "명성황후가 시해된 을미사변(乙未事變) 이후 일본군의 무자비한 공격에 신변에 위협을 느낀 고종과 왕세자가 1896년(건양 1) 2월 11일부터 약 1년간 조선의 왕궁을 떠나 러시아 공관(공사관)에 옮겨 거처한 사건",
        reward = 1002,
    },

    [850] = {
        answer = {"오타와", "요타와"},
        id = 850,
        question = "캐나다의 수도는?",
        reward = 1002,
    },

    [851] = {
        answer = {"브라질리아", "브라질리"},
        id = 851,
        question = "브라질의 수도는?",
        reward = 1002,
    },

    [852] = {
        answer = {"하노이", "하두이"},
        id = 852,
        question = "베트남의 수도는?",
        reward = 1002,
    },

    [853] = {
        answer = {"베를린", "베를리"},
        id = 853,
        question = "독일의 수도는?",
        reward = 1002,
    },

    [854] = {
        answer = {"베른", "베른"},
        id = 854,
        question = "스위스의 수도는?",
        reward = 1002,
    },

    [855] = {
        answer = {"모스크바", "모스크"},
        id = 855,
        question = "러시아의 수도는?",
        reward = 1002,
    },

    [856] = {
        answer = {"런던", "로마"},
        id = 856,
        question = "영국의 수도는?",
        reward = 1002,
    },

    [857] = {
        answer = {"눈에는 눈 이에는 이", "눈에는 눈 이에는 치아"},
        id = 857,
        question = "해를 입은 그대로 앙갚음하는 것을 비유적으로 나타낸 뜻",
        reward = 1002,
    },

    [858] = {
        answer = {"바늘 가는 데 실 간다", "바늘 가는 데 살 간다"},
        id = 858,
        question = "서로 아주 가까운 관계가 있는 것끼리 떨어지지 않고 언제든지 꼭 따르게 된다는 뜻",
        reward = 1002,
    },

    [859] = {
        answer = {"쥐구멍에도 볕 들 날 있다", "쥐구멍에도 해 들 날 있다"},
        id = 859,
        question = "캄캄한 쥐구멍에도 쨍하고 햇볕이 들듯이 고생 끝에는 좋은 시절이 찾아온다는 뜻",
        reward = 1002,
    },

    [860] = {
        answer = {"콩 심은 데 콩 나고 팥 심은 데 팥 난다", "콩 심은 데 콩 나고 팥 심은 데 대파 난다"},
        id = 860,
        question = "모든 일은 원인에 따라서 결과가 생긴다는 뜻",
        reward = 1002,
    },

    [861] = {
        answer = {"발 없는 말이 천 리 간다", "발 없는 말이 만 리 간다"},
        id = 861,
        question = "말은 금방 쉽게 퍼지니 말조심하라는 뜻",
        reward = 1002,
    },

    [862] = {
        answer = {"말 한마디에 천 냥 빚도 갚는다", "말 한마디에 만 냥 빚도 갚는다"},
        id = 862,
        question = "말을 잘하면 천 냥이나 되는 큰 빚을 말로 갚을 수 있다는 말로, 말만 잘하면 어려운 일도 해결할 수 있다는 뜻",
        reward = 1002,
    },

    [863] = {
        answer = {"뱁새가 황새를 따라가면 다리가 찢어진다", "뱁새가 황새를 따라가면 머리가 찢어진다"},
        id = 863,
        question = "자신의 분수에 맞지 않는 힘겨운 짓을 하면 도리어 해만 입는다는 뜻",
        reward = 1002,
    },

    [864] = {
        answer = {"떡 줄 사람은 꿈도 안 꾸는데 김칫국부터 마신다", "뱁새가 황새를 따라가면 머리가 찢어진다"},
        id = 864,
        question = "떡을 가진 사람은 줄 마음도 없는데 떡을 먹을 때 같이 먹는 김칫국부터 마신다는 말. 해 줄 사람은 생각지도 않는데 자기가 넘겨짚어 다 된 줄로 알고 멋대로 행동한다는 뜻",
        reward = 1002,
    },

    [865] = {
        answer = {"어른 말을 들으면 자다가도 떡이 생긴다", "떡 줄 사람은 꿈도 안 꾸는데 김칫국부터 마신다"},
        id = 865,
        question = "어른이 하라는 대로 하면 여러 가지로 이익이 된다는 뜻",
        reward = 1002,
    },

    [866] = {
        answer = {"가는 말이 고와야 오는 말이 곱다", "가는 말이 고와야 오는 손이 곱다"},
        id = 866,
        question = [=[내가 남에게 잘해야 남도 나에게 잘한다는 뜻
화]=],
        reward = 1002,
    },

    [867] = {
        answer = {"낮말은 새가 듣고 밤말은 쥐가 듣는다", "낮말은 새가 듣고 밤말은 귀가 듣는다"},
        id = 867,
        question = "말은 언제나 새어 나가게 마련이니 늘 말조심하라는 뜻",
        reward = 1002,
    },

    [868] = {
        answer = {"말이 씨가 된다", "말이 씨가 된다"},
        id = 868,
        question = "늘 말하던 것이나, 무심코 한 말이 실제로 이루어질 수 있으니 말조심하라는 뜻",
        reward = 1002,
    },

    [869] = {
        answer = {"호랑이도 제 말 하면 온다", "사자도 제 말 하면 온다"},
        id = 869,
        question = "깊은 산속에 사는 호랑이도 자기에 대하여 이야기하면 찾아온다는 뜻",
        reward = 1002,
    },

    [870] = {
        answer = {"원숭이도 나무에서 떨어진다", "원숭이도 다리에서 떨어진다"},
        id = 870,
        question = "일을 아주 잘하는 사람도 때로는 실수할 수 있으니, 너무 속상해할 필요가 없다는 뜻",
        reward = 1002,
    },

    [871] = {
        answer = {"닭 잡아먹고 오리발 내놓기", "닭 잡아먹고 계발 내놓기"},
        id = 871,
        question = "옳지 못한 일을 저질러 놓고 엉뚱한 수작으로 속여 넘기려 한다는 뜻",
        reward = 1002,
    },

    [872] = {
        answer = {"닭 쫓던 개 지붕 쳐다본다", "닭 쫓던 소 지붕 쳐다본다"},
        id = 872,
        question = "애쓰던 일이 실패로 돌아가거나 남보다 뒤떨어져 어찌할 도리가 없다는 뜻",
        reward = 1002,
    },

    [873] = {
        answer = {"독 안에 든 쥐", "독 안에 든 개"},
        id = 873,
        question = "아무리 애를 써도 궁지에서 벗어날 수 없는 처지에 놓였다는 뜻",
        reward = 1002,
    },

    [874] = {
        answer = {"소 잃고 외양간 고친다", "말 잃고 외양간 고친다"},
        id = 874,
        question = "일이 이미 잘못된 뒤에는 손을 써도 소용이 없다는 뜻",
        reward = 1002,
    },

    [875] = {
        answer = {"우물을 파도 한 우물만 파라", "독 안에 든 쥐"},
        id = 875,
        question = "하던 일을 자주 바꾸어 하면 아무 성과가 없으니, 한 가지 일을 끝까지 하여야 성공할 수 있다는 뜻",
        reward = 1002,
    },

    [876] = {
        answer = {"하룻강아지 범 무서운 줄 모른다", "독 안에 든 쥐"},
        id = 876,
        question = "아무 경험 없는 사람이 철없이 함부로 덤빈다는 뜻",
        reward = 1002,
    },

    [877] = {
        answer = {"MZ세대", "M세대"},
        id = 877,
        question = "1980년대 초~2000년대 초 출생한 밀레니얼 세대와 1990년대 중반~2000년대 초반 출생한 Z세대를 통칭하는 말",
        reward = 1002,
    },

    [878] = {
        answer = {"Z세대", "ZM세대"},
        id = 878,
        question = "990년대 중반에서 2000년대 초반에 걸쳐 태어난 젊은 세대를 이르는 말",
        reward = 1002,
    },

    [879] = {
        answer = {"X세대", "M세대"},
        id = 879,
        question = "무관심ㆍ무정형ㆍ기존 질서 부정 등을 특징으로 하는 1965년∼1976년 사이에 출생한 세대",
        reward = 1002,
    },

    [880] = {
        answer = {"Y세대", "M세대"},
        id = 880,
        question = "미국에서 제2차 세계대전 이후 1946~1965년 사이에 출생한 베이비붐 세대의 자녀를 뜻",
        reward = 1002,
    },

    [881] = {
        answer = {"N세대", "M세대"},
        id = 881,
        question = "N세대는 1970년대 중반 이후에 태어나 경제적 혜택과 문화적 혜택을 동시에 누린 X세대 중에서도 특히 컴퓨터에 익숙한 세대를 가리키는 말",
        reward = 1002,
    },

    [882] = {
        answer = {"오렌지족", "요렌지족"},
        id = 882,
        question = "부모 세대가 이룩한 물질적 풍요를 바탕으로 서울 강남 일대에서 퇴폐적인 소비 문화를 즐기는 젊은이들",
        reward = 1002,
    },

    [883] = {
        answer = {"캥거루족", "캥거무족"},
        id = 883,
        question = "우리나라가 IMF관리체제 아래 있던 시절 대학가에서 유행하던 신조어",
        reward = 1002,
    },

    [884] = {
        answer = {"니트족", "마트족"},
        id = 884,
        question = "학생도 아니고 직장인도 아니면서 직업 훈련도 받지 않는 근로의욕 없는 청년 무직자를 가리키는 말",
        reward = 1002,
    },

    [885] = {
        answer = {"프리터족", "프리마족"},
        id = 885,
        question = "필요한 돈이 모일 때까지만 아르바이트로 일하는 사람들",
        reward = 1002,
    },

    [886] = {
        answer = {"프리커족", "프리마족"},
        id = 886,
        question = "보통 1~2년 동안 일을 하며 모은 돈으로 1~2년 동안 쉬면서 자기계발이나 자신이 하고 싶은 취미 등을 누리는 새로운 노동계층을 뜻",
        reward = 1002,
    },

    [887] = {
        answer = {"빌런", "빌런마"},
        id = 887,
        question = "최근에는 무언가에 집착하거나 특이한 행동을 하는 이들을 가리키는 의미",
        reward = 1002,
    },

    [888] = {
        answer = {"탈룰라", "탈룰라무"},
        id = 888,
        question = [=[대화 도중 의도치 않게 상대방의 가족을 욕되게 한 화자가 상황을 재치 있게 무마하고자 하는 변명을 가리키는 신조어
화]=],
        reward = 1002,
    },

    [889] = {
        answer = {"맥거핀", "맥거핀무"},
        id = 889,
        question = "영화에서 중요한 것처럼 등장하지만 실제로는 줄거리에 영향을 미치지 않는 극적 장치를 뜻",
        reward = 1002,
    },

    [890] = {
        answer = {"클리셰", "클리셰마"},
        id = 890,
        question = "진부한 표현이나 고정관념을 뜻하는 프랑스어로, 영화나 드라마 등에서 진부한 장면이나 판에 박힌 대화, 상투적 줄거리, 전형적인 수법이나 표현을 뜻",
        reward = 1002,
    },

    [891] = {
        answer = {"오마쥬", "오마쥬스"},
        id = 891,
        question = "예술작품의 경우 어떤 작품이 다른 작품에 대한 존경의 표시로 일부러 모방을 하거나, 기타 다른 형태의 인용을 하는 것을 가리킬 때 쓰는 말",
        reward = 1002,
    },

    [892] = {
        answer = {"미장센", "미장센스"},
        id = 892,
        question = "연극과 영화 등에서 연출가가 무대 위의 모든 시각적 요소들을 배열하는 작업",
        reward = 1002,
    },

    [893] = {
        answer = {"헌집증후근", "헌집증"},
        id = 893,
        question = "오래된 집이 건강에 나쁜 영향을 주는 현상",
        reward = 1002,
    },

    [894] = {
        answer = {"빌딩공해", "빌딩공"},
        id = 894,
        question = "대도시에 빌딩이 밀집되면서 야기되는 공해",
        reward = 1002,
    },

    [895] = {
        answer = {"빌딩증후근", "빌딩증"},
        id = 895,
        question = "밀폐된 공간의 오염된 공기가 원인이 되어 짜증이 나고 온몸이 쉽게 피로해지는 현상",
        reward = 1002,
    },

    [896] = {
        answer = {"새집증후군", "새집증"},
        id = 896,
        question = "새로 지은 건물 안에서 거주자들이 느끼는 건강상 문제 및 불쾌감을 이르는 용어",
        reward = 1002,
    },

    [897] = {
        answer = {"SNS", "MSN"},
        id = 897,
        question = "웹상에서 이용자들이 인적 네트워크를 형성할 수 있게 해주는 서비스",
        reward = 1002,
    },

    [898] = {
        answer = {"사이버 범죄", "사이브 범죄"},
        id = 898,
        question = "컴퓨터 통신 등을 악용하여 사이버 공간에서 행하는 범죄",
        reward = 1002,
    },

    [899] = {
        answer = {"사이버 명예훼손죄", "사이 명예훼손죄"},
        id = 899,
        question = "정보통신망 이용촉진 및 정보보호 등에 관한 법률'에 따라 사람을 비방할 목적으로 정보통신망을 통해 공공연하게 사실이나 거짓의 사실을 드러내어, 타인의 명예를 훼손한 자에 대해 적용하는 죄목",
        reward = 1002,
    },

    [900] = {
        answer = {"사이버 모욕죄", "사이 모욕죄"},
        id = 900,
        question = "네티즌들이 인터넷을 통한 허위사실 유포 등으로 특정인의 인격을 모독하고 명예를 심각하게 훼손하는 것",
        reward = 1002,
    },

    [901] = {
        answer = {"사이버 성폭력", "사이 성폭력"},
        id = 901,
        question = "인터넷 등 사이버 공간상에서 발생하는 폭력행사의 행위로 성적인 메시지 전달, 성적 대화 요청 및 성적인 문제와 관련하여 개인신상에 관한 정보게시 등의 방식을 통하여 상대방의 의지와 관계없이 상대방을 위협하거나, 괴롭히는 행위",
        reward = 1002,
    },

    [902] = {
        answer = {"사이버 스토킹", "사이 스토킹"},
        id = 902,
        question = "이동통신·이메일·대화방·게시판 등의 정보통신망을 이용해 의도와 악의를 가지고 지속적으로 공포감·불안감 등을 유발하는 행위",
        reward = 1002,
    },

    [903] = {
        answer = {"디지털 원주민", "디털 원주민"},
        id = 903,
        question = "디지털기기를 태어나면서부터 자연스럽게 접함으로써 자유자재로 사용하는 세대를 지칭",
        reward = 1002,
    },

    [904] = {
        answer = {"사이버 불링", "사이 불링"},
        id = 904,
        question = "“컴퓨터나 스마트폰 등의 전자 매체를 통해 의도성을 갖고 반복적으로 욕설과 협박, 괴롭힘을 가하는 행위, SNS 공간에서 폭언이나 음란물을 보내는 행위, 그리고 피해자에 대한 거짓 정보를 유포하는 행위”",
        reward = 1002,
    },

    [905] = {
        answer = {"사이버 따돌림", "사이 따돌림"},
        id = 905,
        question = "특정인을 온라인 상에서 집단적으로 집요하게 괴롭히는 행위",
        reward = 1002,
    },

    [906] = {
        answer = {"사이버 슬래킹", "사이 슬래킹"},
        id = 906,
        question = "근무 시간에 주식거래나 게임 등 업무 이외의 용도로 인터넷을 사용함으로써 업무에 방해가 되는 일체의 행위",
        reward = 1002,
    },

    [907] = {
        answer = {"사이버 코쿤족", "사이 코쿤족"},
        id = 907,
        question = "사이버 공간에서 각종 정보를 얻는 차원을 넘어 정서적 만족감까지 느끼는 세대",
        reward = 1002,
    },

    [908] = {
        answer = {"리뷰슈머", "리뷰슈"},
        id = 908,
        question = "인터넷 블로그나 게시판에 전문적으로 글을 올리는 사람",
        reward = 1002,
    },

    [909] = {
        answer = {"트윈슈머", "트윈슈"},
        id = 909,
        question = "터넷의 사용후기를 참고하여 물건을 구매하는 소비자",
        reward = 1002,
    },

    [910] = {
        answer = {"트라이슈머", "트라이슈"},
        id = 910,
        question = "관습이나 광고에 얽매이지 않고 항상 새로운 무언가를 시도하는 '체험적 소비자' 집단",
        reward = 1002,
    },

    [911] = {
        answer = {"프로슈머", "프로슈"},
        id = 911,
        question = "생산에 참여하는 소비자",
        reward = 1002,
    },

    [912] = {
        answer = {"블랙 컨슈머", "블랙 컨슈"},
        id = 912,
        question = "기업 등을 상대로 부당한 이익을 취하고자 제품을 구매한 후 고의적으로 악성 민원을 제기하는 자",
        reward = 1002,
    },

    [913] = {
        answer = {"크리슈머", "크리슈"},
        id = 913,
        question = "기존 제품을 자신에 맞게 새롭게 창조하는 소비자",
        reward = 1002,
    },

    [914] = {
        answer = {"맨슈머", "맨슈"},
        id = 914,
        question = "소비에 적극적이고 자기 취향이 확실한 남성 소비자",
        reward = 1002,
    },

    [915] = {
        answer = {"모디슈머", "모디슈"},
        id = 915,
        question = "제품을 제조사에서 제시하는 표준방법대로 따르지 않고 자신만의 방식으로 재창조해 내는 소비자",
        reward = 1002,
    },

    [916] = {
        answer = {"메타슈머", "메타슈"},
        id = 916,
        question = "평범한 제품을 구입하여 스스로 제품에 변화를 가해 새로운 제품으로 변화시키려는 소비자",
        reward = 1002,
    },

    [917] = {
        answer = {"큐레이슈머", "큐레이슈"},
        id = 917,
        question = "전시회의 큐레이터처럼 스스로 삶을 꾸미고 연출하는 데 능수능란한 편집형 소비자",
        reward = 1002,
    },

    [918] = {
        answer = {"만구성비", "만구성"},
        id = 918,
        question = "만인이 칭찬하면 명성이 길이 남게 됨",
        reward = 1002,
    },

    [919] = {
        answer = {"설왕설래", "설왕설"},
        id = 919,
        question = "말들이 왔다 갔다 함. 즉 의견이나 입장이 달라 말로 옥신각신하는 모습",
        reward = 1002,
    },

    [920] = {
        answer = {"갑론을박", "갑론을"},
        id = 920,
        question = "서로 자기 의견을 내세우고 다른 사람의 의견을 반박하는 모습",
        reward = 1002,
    },

    [921] = {
        answer = {"중구난방", "중구난"},
        id = 921,
        question = "여러 사람이 각기 말하는 의견이 봇물 터지듯 쏟아져 나옴",
        reward = 1002,
    },

    [922] = {
        answer = {"왈가왈부", "왈가말부"},
        id = 922,
        question = "혹은 옳다고 하고 혹은 그르다고 함",
        reward = 1002,
    },

    [923] = {
        answer = {"왈리왈시", "왈리말시"},
        id = 923,
        question = "배 놔라 감 놔라 함. 남의 일에 쓸데없이 간섭하는 모습",
        reward = 1002,
    },

    [924] = {
        answer = {"유언비어", "유언비"},
        id = 924,
        question = "근거 없이 이리저리 떠도는 헛된 소문",
        reward = 1002,
    },

    [925] = {
        answer = {"사실무근", "사실무관"},
        id = 925,
        question = "근거가 없음. 사실과 다름",
        reward = 1002,
    },

    [926] = {
        answer = {"호언장담", "호언장념"},
        id = 926,
        question = "주위 상황을 고려함이 없이 자신 있게 큰소리치는 모습",
        reward = 1002,
    },

    [927] = {
        answer = {"단도직입", "단도직념"},
        id = 927,
        question = "군말이나 인사말 따위 없이 곧장 요지를 말함",
        reward = 1002,
    },

    [928] = {
        answer = {"고래 싸움에 새우 등 터진다", "고래 싸움에 거부기 등 터진다"},
        id = 928,
        question = "강한 사람끼리 싸우는 통에 약한 사람이 해를 입게 된다는 뜻",
        reward = 1002,
    },

    [929] = {
        answer = {"굼벵이도 구르는 재주가 있다", "고래 싸움에 새우 등 터진다"},
        id = 929,
        question = "아무리 능력이 없는 사람이라도 한 가지 재주는 있다는 뜻",
        reward = 1002,
    },

    [930] = {
        answer = {"꿩 먹고 알 먹는다", "꿩 먹고 약 먹는다"},
        id = 930,
        question = "한 가지 일로 두 가지 이익을 본다는 뜻",
        reward = 1002,
    },

    [931] = {
        answer = {"우물 안 개구리", "우물 안 너구리"},
        id = 931,
        question = "넓은 세상을 알지 못하고 저만 잘난 줄 아는 사람을 비꼬는 말",
        reward = 1002,
    },

    [932] = {
        answer = {"원숭이도 나무에서 떨어진다", "우물 안 너구리"},
        id = 932,
        question = "아무리 익숙하고 잘하는 사람도 가끔 실수할 때가 있다는 뜻",
        reward = 1002,
    },

    [933] = {
        answer = {"하룻강아지 범 무서운 줄 모른다", "꿩 먹고 약 먹는다"},
        id = 933,
        question = "아무 경험 없는 사람이 철없이 함부로 덤비는 경우를 이르는 말",
        reward = 1002,
    },

    [934] = {
        answer = {"소 잃고 외양간 고친다", "말 잃고 외양간 고친다"},
        id = 934,
        question = "일이 이미 잘못된 뒤에는 손을 써도 소용이 없다는 뜻",
        reward = 1002,
    },

    [935] = {
        answer = {"호랑이도 제 말 하면 온다", "사자도 제 말 하면 온다"},
        id = 935,
        question = "깊은 산속에 사는 호랑이도 자기에 대하여 이야기하면 찾아온다는 말, 없는 사람에 대해 말하면 등장한다는 뜻",
        reward = 1002,
    },

    [936] = {
        answer = {"가는 토끼 잡으려다 잡은 토끼 놓친다", "가는 토끼 잡으려다 잡은 사슴 놓친다"},
        id = 936,
        question = "잡아 놓은 토끼가 있는데도 욕심을 부린 나머지 지나가는 또 다른 토끼를 잡으려다가 잡아 놓은 토끼마저 놓쳐 버린다는 말",
        reward = 1002,
    },

    [937] = {
        answer = {"개천에서 용 난다", "개천에서 새 난다"},
        id = 937,
        question = "어려운 환경에서도 훌륭한 사람이 나올 수 있다는 말",
        reward = 1002,
    },

    [938] = {
        answer = {"피는 물보다 진하다", "피는 먹보다 진하다"},
        id = 938,
        question = "가족끼리 정이 깊다는 뜻",
        reward = 1002,
    },

    [939] = {
        answer = {"고슴도치도 제 자식이 제일 곱다", "고슴도치도 제 남편이 제일 곱다"},
        id = 939,
        question = "남들 눈에는 예뻐 보이지 않아도 모든 부모에게는 자기 자식이 가장 예뻐 보인다는 뜻",
        reward = 1002,
    },

    [940] = {
        answer = {"열 손가락 깨물어 안 아픈 손가락 없다", "열 손가락 깨물어 안 아픈 발가락 없다"},
        id = 940,
        question = "열 손가락 중에 깨물어서 아프지 않은 손가락이 없듯이, 부모는 자식이 많아도 전부 소중하게 여긴다는 말",
        reward = 1002,
    },

    [941] = {
        answer = {"자식을 길러 봐야 부모 사랑을 안다", "자식을 길러 봐야  사랑을 안다"},
        id = 941,
        question = "부모님의 마음은 내가 부모가 되어 보아야 비로소 알 수 있다는 뜻",
        reward = 1002,
    },

    [942] = {
        answer = {"부모 말을 들으면 자다가도 떡이 생긴다", "자식을 길러 봐야  사랑을 안다"},
        id = 942,
        question = "부모의 말을 잘 듣고 따르면 실수가 적고 좋은 일이 생긴다는 뜻",
        reward = 1002,
    },

    [943] = {
        answer = {"윗물이 맑아야 아랫물도 맑다", "윗물이 맑아야 강물도 맑다"},
        id = 943,
        question = "윗사람이 바른 행동을 해야 아랫사람도 바르게 행동한다는 뜻",
        reward = 1002,
    },

    [944] = {
        answer = {"누이 좋고 매부 좋다", "누이 좋고 매형 좋다"},
        id = 944,
        question = "어떤 일이 양쪽 모두에게 이롭다는 뜻",
        reward = 1002,
    },

    [945] = {
        answer = {"아이 보는 데서는 찬물도 못 마신다", "아이 보는 데서는 물도 못 마신다"},
        id = 945,
        question = "아이들은 어른의 행동을 따라하기 때문에 말과 행동을 조심하라는 뜻",
        reward = 1002,
    },

    [946] = {
        answer = {"부모가 착해야 효자가 난다", "부모가 착해야 효녀가 난다"},
        id = 946,
        question = "부모가 착하면, 보고 배운 바가 있어서 자식도 착한 사람이 된다는 뜻",
        reward = 1002,
    },

    [947] = {
        answer = {"효성이 지극하면 돌 위에서도 풀이 난다", "효성이 지극하면 돌 배에서도 풀이 난다"},
        id = 947,
        question = "어떤 조건에서도 자식으로서 해야 할 일을 다해야 한다는 뜻",
        reward = 1002,
    },

    [948] = {
        answer = {"고생 끝에 낙이 온다", "고생 끝에 복이 온다"},
        id = 948,
        question = "어려운 일을 겪고 난 뒤에는 반드시 좋은 일이 생긴다는 말",
        reward = 1002,
    },

    [949] = {
        answer = {"공든 탑이 무너지랴", "공든 탑이 사라지랴"},
        id = 949,
        question = [=[어떤 일을 정성을 다해 오랫동안 하면 반드시 좋은 결과를 얻는다는 뜻
화]=],
        reward = 1002,
    },

    [950] = {
        answer = {"구르는 돌에는 이끼가 안 낀다", "구르는 돌에는 풀이 안 낀다"},
        id = 950,
        question = "부지런하고 꾸준히 노력하는 사람은 계속 발전한다는 뜻",
        reward = 1002,
    },

    [951] = {
        answer = {"흐르는 물은 썩지 않는다", "흐르는 물은 잠들지 않는다"},
        id = 951,
        question = "부지런히 일하고 공부하며 계속 자신을 단련해야 한다는 말",
        reward = 1002,
    },

    [952] = {
        answer = {"열 번 찍어 안 넘어가는 나무 없다", "열 번 찍어  넘어가는 나무 없다"},
        id = 952,
        question = "고집이 센 사람도 여러 번 권하고 달래면 결국 마음이 변한다는 뜻",
        reward = 1002,
    },

    [953] = {
        answer = {"콩 심은 데 콩 나고 팥 심은 데 팥 난다", "콩 심은 데 콩 나고 팥 심은 데 대파 난다"},
        id = 953,
        question = "모든 일의 결과는 그것의 원인에 따른 것이라는 말",
        reward = 1002,
    },

    [954] = {
        answer = {"티클 모아 태산", "티클 모아 태산봉"},
        id = 954,
        question = "아무리 작은 것이라도 모으고 또 모으면 나중에 큰 것이 된다는 말",
        reward = 1002,
    },

    [955] = {
        answer = {"구슬이 서 말이라도 꿰어야 보배", "구슬이 서 말이라도 꿰어야 보배단지"},
        id = 955,
        question = "무엇이든 다듬고 쓸모 있게 만들어야 값진 보배가 된다는 뜻",
        reward = 1002,
    },

    [956] = {
        answer = {"하늘을 보아야 별을 따지", "하늘을 보아야 별을 따지"},
        id = 956,
        question = "어떤 일에서 좋은 성과를 얻으려면 그 일이 필요로 하는 노력과 준비를 해야 한다는 말",
        reward = 1002,
    },

    [957] = {
        answer = {"입에 들어가는 밥술도 제가 떠 넣어야 한다", "입에 들어가는 밥풀도 제가 떠 넣어야 한다"},
        id = 957,
        question = "아무리 쉬운 일이라도 자기 스스로 노력해야 이룰 수 있다는 말",
        reward = 1002,
    },

    [958] = {
        answer = {"새도 가지를 가려 앉는다", "새끼도 가지를 가려 앉는다"},
        id = 958,
        question = "새가 쉴 때 나뭇가지를 가려 앉듯이, 친구를 사귈 때 신중히 사귀라는 뜻",
        reward = 1002,
    },

    [959] = {
        answer = {"가제는 게 편", "가제는 개 편"},
        id = 959,
        question = "모습이나 상황이 비슷한 친구끼리 서로 돕거나 편을 들어준다는 뜻",
        reward = 1002,
    },

    [960] = {
        answer = {"먹을 가까이하면 검어진다", "먹을 멀리하면 검어진다"},
        id = 960,
        question = "먹을 만지면 손이 검게 물들 듯, 나쁜 사람 옆에 있으면 그 사람의 나쁜 모습을 닮게 된다는 뜻",
        reward = 1002,
    },

    [961] = {
        answer = {"바늘 가는 데 실 간다", "바늘 가는 데 살 간다"},
        id = 961,
        question = "떨어지지 않고 서로 꼭 붙어 다니는 가까운 사이를 말할 때",
        reward = 1002,
    },

    [962] = {
        answer = {"친구는 옛 친구가 좋고 옷은 새옷이 좋다", "친구는 옛 친구가 좋고 옷은 것옷이 좋다"},
        id = 962,
        question = "오래 사귄 친구일수록 함께한 추억도 많고, 정도 들어서 좋다는 말",
        reward = 1002,
    },

    [963] = {
        answer = {"친구 따라 강남 간다", "친구 따라 장가 간다"},
        id = 963,
        question = "친구가 가니까 멀고 익숙하지 않은 곳인데도 따라간다는 말",
        reward = 1002,
    },

    [964] = {
        answer = {"물이 너무 맑으면 고기가 안 모인다", "물이 너무 맑으면 고기가 모인다"},
        id = 964,
        question = "사람이 지나치게 바르고 허물이 없으면, 곁에 사람들이 따르지 않는다는 뜻",
        reward = 1002,
    },

    [965] = {
        answer = {"도토리 키 재기", "도토리 키 보기"},
        id = 965,
        question = "비슷한 크기의 도토리들이 크기를 재듯, 실력이 비슷한 사람들끼리 서로 겨루는 모습을 표현할 때",
        reward = 1002,
    },

    [966] = {
        answer = {"길동무가 좋으면 먼 길도 가깝다", "길동무가 좋으면 길도 가깝다"},
        id = 966,
        question = "서로 마음이 통하는 친구와 함께한다면, 무엇을 하든 신이 나고 힘도 덜 든다는 뜻",
        reward = 1002,
    },

    [967] = {
        answer = {"고슴도치도 살 친구가 있다", "고슴도치도 친구가 있다"},
        id = 967,
        question = "누구에게나 친하게 지낼 친구가 있기 마련이라는 뜻",
        reward = 1002,
    },

    [968] = {
        answer = {"엔도르핀", "엔도르"},
        id = 968,
        question = "동물의 뇌 등에서 추출되는 모르핀과 같은 진통효과를 가지는 물질의 총칭",
        reward = 1002,
    },

    [969] = {
        answer = {"도파민", "도파민이"},
        id = 969,
        question = "신경전달물질하나로 노르에피네프린과 에피네프린 합성체의 전구물질(前驅物質)이다. 동식물에 존재하는 아미노산의 하나이며 뇌신경 세포의 흥분 전달 역할",
        reward = 1002,
    },

    [970] = {
        answer = {"코르티솔", "코르티솔드"},
        id = 970,
        question = "급성 스트레스에 반응해 분비되는 물질로, 스트레스에 대항하는 신체에 필요한 에너지를 공급해 주는 역할",
        reward = 1002,
    },

    [971] = {
        answer = {"세로토닌", "세로토닌다"},
        id = 971,
        question = "뇌의 시상하부 중추에 존재하는 신경전달물질로 기능하는 화학물질 중 하나",
        reward = 1002,
    },

    [972] = {
        answer = {"DHEA", "DHE"},
        id = 972,
        question = "콩팥 바로 위에 있는 부신에서 대부분 생산되는 스테로이드 호르몬",
        reward = 1002,
    },

    [973] = {
        answer = {"안드로겐", "안드로겐드"},
        id = 973,
        question = "남성 생식계의 성장과 발달에 영향을 미치는 호르몬의 총칭으로 남성호르몬",
        reward = 1002,
    },

    [974] = {
        answer = {"모내기", "모내기마"},
        id = 974,
        question = "못자리에서 기른 모를 본논에 옮겨 심는 일",
        reward = 1002,
    },

    [975] = {
        answer = {"이앙법", "이앙법부"},
        id = 975,
        question = "벼농사에서, 못자리에서 모를 어느 정도 키운 다음에 그 모를 본논으로 옮겨 심는 재배방법",
        reward = 1002,
    },

    [976] = {
        answer = {"이모작", "이모작"},
        id = 976,
        question = "동일한 농장에 두 종류의 농작물을 1년 중 서로 다른 시기에 재배하는 농법",
        reward = 1002,
    },

    [977] = {
        answer = {"시비법", "시버법"},
        id = 977,
        question = "토양이나 작물에 비료성분을 공급하여 농작물의 생육을 촉진시키는 농작법",
        reward = 1002,
    },

    [978] = {
        answer = {"수경재배", "수정재배"},
        id = 978,
        question = "흙을 사용하지 않고 물과 수용성 영양분으로 만든 배양액 속에서 식물을 키우는 방법을 일컫는 말",
        reward = 1002,
    },

    [979] = {
        answer = {"양액재배", "양역재배"},
        id = 979,
        question = "작물의 생육에 필요한 양분을 수용액으로 만들어 재배하는 방법",
        reward = 1002,
    },

    [980] = {
        answer = {"점적재배", "점적재배류"},
        id = 980,
        question = "파이프나 호스로 물을 끌어 올려 농작물이 필요로 하는 양만큼의 물과 양분을 작물의 뿌리에 방울 방울 뿌려 농작물을 재배하는 방법",
        reward = 1002,
    },

    [981] = {
        answer = {"개량삼포식", "개량삼포"},
        id = 981,
        question = "토지의 지력쇠퇴를 방지하고 노력의 분배를 합리화하려는 돌려짓기 영농방법",
        reward = 1002,
    },

    [982] = {
        answer = {"녹비", "녹비누"},
        id = 982,
        question = "녹색식물의 줄기와 잎을 비료로 사용하는 것",
        reward = 1002,
    },

    [983] = {
        answer = {"건지농법", "건지공법"},
        id = 983,
        question = "연간 강우량이 적은 지대에서 농경지에 인위적인 관개를 하지 않고 작물을 재배하는 방식",
        reward = 1002,
    },

    [984] = {
        answer = {"질소비료", "질소비료액"},
        id = 984,
        question = "질소를 많이 포함하고 있는 비료. 공기로부터 질소를 분리하여 만든다.",
        reward = 1002,
    },

    [985] = {
        answer = {"산성비료", "산성비료물"},
        id = 985,
        question = "비료의 화학적 반응에 따른 분류로 비료가 물에 녹았을 때 산성을 나타내는 비료",
        reward = 1002,
    },

    [986] = {
        answer = {"고토석회", "고토석회모"},
        id = 986,
        question = "석회질 비료",
        reward = 1002,
    },

    [987] = {
        answer = {"가라비료", "가라비료액"},
        id = 987,
        question = "가리를 주성분으로 하는 비료로서 황산가리, 염화가리, 황산가리 등이 있음. K2O로 성분함량을 표기함.",
        reward = 1002,
    },

    [988] = {
        answer = {"염화가리", "염화가리물"},
        id = 988,
        question = "염소와 가리의 화합물로서 염소와 칼륨, 탄산갈륨의 총칭.",
        reward = 1002,
    },

    [989] = {
        answer = {"탄산가리", "탄산가리물"},
        id = 989,
        question = "탄산칼륨(炭酸 Kalium)의 구칭.",
        reward = 1002,
    },

    [990] = {
        answer = {"염기성 비료", "염기성 비료소"},
        id = 990,
        question = "비료의 화학적 반응에 따른 분류로, 비료를 녹인 물의 반응이 염기성을 나타내는 비료를 가리킨다.",
        reward = 1002,
    },

    [991] = {
        answer = {"중성비료", "중성비료소"},
        id = 991,
        question = "비료의 화학적 반응에 따른 분류로 중성반응을 나타내는 비료",
        reward = 1002,
    },

    [992] = {
        answer = {"무기질비료", "무기질비료소"},
        id = 992,
        question = "비료를 원료에 의해서 분류했을 때, 비료 성분을 무기화합물의 형태로 함유하고 있는 화학비료.",
        reward = 1002,
    },

    [993] = {
        answer = {"맥주", "소주"},
        id = 993,
        question = "특허청에 상표등록이 더 많은 술은?",
        reward = 1002,
    },

    [994] = {
        answer = {"가는 말에 채찍질한다", "가는 말에 찍질한다"},
        id = 994,
        question = "열심히 하는 일을 더 빨리하라고 부추긴다는 말",
        reward = 1002,
    },

    [995] = {
        answer = {"개구리 올챙이 적 생각 못 한다", "개구리 올챙이 적 생각  한다"},
        id = 995,
        question = "형편이 전보다 나아졌다고 하여 지난날의 어려웠던 때를 생각하지 않고 처음부터 잘난 듯이 뽐낸다는 말",
        reward = 1002,
    },

    [996] = {
        answer = {"개구리도 옴쳐야 뛴다", "개구리도 옴쳐야 난다"},
        id = 996,
        question = "어떤 일을 할 때 아무리 급하더라도 일을 이루려면 그 일을 위하여 준비할 시간이 있어야 한다는 말",
        reward = 1002,
    },

    [997] = {
        answer = {"개발에 편자", "개발에 편지"},
        id = 997,
        question = "개발에 *편자가 어울리지 않듯, 차림이나 지닌 물건 따위가 제격에 맞지 않는다는 말",
        reward = 1002,
    },

    [998] = {
        answer = {"고래 싸움에 새우 등 터진다", "고래 싸움에 거부기 등 터진다"},
        id = 998,
        question = "몸집이 큰 고래가 싸우면 그사이에 낀 작은 새우는 아무 상관이 없는데도 등이 터진다는 말",
        reward = 1002,
    },

    [999] = {
        answer = {"고양이 쥐 생각해 준다", "고양이 생선 생각해 준다"},
        id = 999,
        question = "속으로는 해칠 마음을 품고 있으면서 겉으로는 생각해 주는 척한다는 말",
        reward = 1002,
    },

    [1000] = {
        answer = {"구렁이 담 넘어가듯", "구렁이 장 넘어가듯"},
        id = 1000,
        question = "일을 처리하는 데 태도를 분명하게 하지 않고 슬그머니 얼버무린다는 말",
        reward = 1002,
    },

    [1001] = {
        answer = {"굼벵이도 구르는 재주가 있다", "굼벵이도 구르는 재롱이 있다"},
        id = 1001,
        question = "능력이 모자라는 사람이 남의 관심을 끌 만한 행동을 함을 놀림조로 이르는 말",
        reward = 1002,
    },

    [1002] = {
        answer = {"까마귀 날자 배 떨어진다", "까마귀 날자 사과 떨어진다"},
        id = 1002,
        question = "아무 상관도 없는 일이 공교롭게도 동시에 일어나 어떤 관계가 있는 것처럼 의심을 받게 된다는 말",
        reward = 1002,
    },

    [1003] = {
        answer = {"꽁지 빠진 새 같다", "꽁지 빠진 닭 같다"},
        id = 1003,
        question = "꼴이 초라해 보인다는 말",
        reward = 1002,
    },

    [1004] = {
        answer = {"다람쥐 쳇바퀴 돌듯 한다", "쥐 쳇바퀴 돌듯 한다"},
        id = 1004,
        question = "발전하지 못하고 똑같은 일만 되풀이해서 한다는 말",
        reward = 1002,
    },

    [1005] = {
        answer = {"닭 쫓던 개 지붕 쳐다본다", "닭 쫓던 소 지붕 쳐다본다"},
        id = 1005,
        question = "닭을 쫓던 개가 닭이 지붕으로 올라가자 쫓아 올라가지 못하고 지붕만 쳐다본다는 말",
        reward = 1002,
    },

    [1006] = {
        answer = {"드문드문 걸어도 황소걸음", "드문드문 걸어도 소걸음"},
        id = 1006,
        question = "황소걸음처럼 느리더라도 그것이 오히려 믿음직스럽고 알차다는 말",
        reward = 1002,
    },

    [1007] = {
        answer = {"물고기는 물을 떠나 살 수 없다", "물고기는 강물을 떠나 살 수 없다"},
        id = 1007,
        question = "활동하는 데에 자신에게 걸맞는 터전이 있다는 말",
        reward = 1002,
    },

    [1008] = {
        answer = {"소도 언덕이 있어야 비빈다", "소도 덕이 있어야 비빈다"},
        id = 1008,
        question = "언덕이 있어야 소도 가려운 곳을 비빌 수 있다는 말",
        reward = 1002,
    },

    [1009] = {
        answer = {"약빠른 고양이 밤눈 어둡다", "약빠른 고양이 밤 어둡다"},
        id = 1009,
        question = "꾀가 있고 눈치가 빨라 실수가 없을 것 같은 사람도 부족한 점은 있다는 말",
        reward = 1002,
    },

    [1010] = {
        answer = {"용의 꼬리보다 뱀의 머리가 낫다", "용의 꼬리보다 뱀의 꼬리가 낫다"},
        id = 1010,
        question = "크고 훌륭한 사람의 뒤를 쫓아다니는 것보다는 작고 보잘것없는 데서 남의 우두머리가 낫다는 말은?",
        reward = 1002,
    },

    [1011] = {
        answer = {"황소 뒷걸음치다가 쥐 잡는다", "황소 뒷걸음치다가 뱀 잡는다"},
        id = 1011,
        question = "어쩌다 우연히 일이 이루어지거나 알아맞힌다는 말",
        reward = 1002,
    },

    [1012] = {
        answer = {"제비는 작아도 강남 간다", "제비는 작아도 남방 간다"},
        id = 1012,
        question = "강남은 남쪽의 먼 나라를 가리키는 말",
        reward = 1002,
    },

    [1013] = {
        answer = {"평화", "승리"},
        id = 1013,
        question = "노벨상 부문6개는 어떤 것들이 있나?",
        reward = 1002,
    },

    [1014] = {
        answer = {"의학", "교류"},
        id = 1014,
        question = "노벨상 부문6개는 어떤 것들이 있나?",
        reward = 1002,
    },

    [1015] = {
        answer = {"문학", "지리"},
        id = 1015,
        question = "노벨상 부문6개는 어떤 것들이 있나?",
        reward = 1002,
    },

    [1016] = {
        answer = {"물리학", "역사"},
        id = 1016,
        question = "노벨상 부문6개는 어떤 것들이 있나?",
        reward = 1002,
    },

}
