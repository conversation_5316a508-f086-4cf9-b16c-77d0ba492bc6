-- ./excel/huodong/treasure/dialog.xlsx
return {

    [100] = {
        content = "미니 게임을 해요",
        dialog_id = 100,
        last_action = {{["content"] = "시작하기", ["event"] = "무시하기"}},
        next = "0",
        pre_id_list = "0",
        status = 2,
        subid = 1,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [101] = {
        content = "꽥꽥꽥,두뇌 게임을 재미있게 해요. n룰 소개\n참가 인원에 따라 던전에 상응하는 수의 환상이 나타납니다.\n정해진 시간에 모든 환상 소멸하면 클리어 보상을,완료하지 못하면 참여 보상을 획득합니다.",
        dialog_id = 101,
        last_action = {{["content"] = "던전 입장하기", ["event"] = "ENTER"}, {["content"] = "친구 소환하기", ["event"] = "CP"}, {["content"] = "무시하기"}},
        next = "0",
        pre_id_list = "0",
        status = 2,
        subid = 1,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [102] = {
        content = "팀원이 다 차지 않았습니다,던전에 입장하시겠나요?",
        dialog_id = 102,
        last_action = {{["content"] = "확인", ["event"] = "CONFIRM"}, {["content"] = "고민하기", ["event"] = "DI101"}},
        next = "0",
        pre_id_list = "0",
        status = 2,
        subid = 1,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [103] = {
        content = "참가 인원에 따라 던전에 상응하는 수의 환상이 나타납니다.\n정해진 시간에 모든 환상을 소멸하면 클리어 보상을,완료하지 못하면 참여 보상을 획득합니다.",
        dialog_id = 103,
        last_action = {{["content"] = "던전 입장하기", ["event"] = "ENTER"}, {["content"] = "친구 소환하기", ["event"] = "CP"}, {["content"] = "플레이 룰", ["event"] = "DI103"}},
        next = "0",
        pre_id_list = "0",
        status = 2,
        subid = 1,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [300] = {
        content = "꽥꽥꽥,게임을 시작할 수 있습니다.",
        dialog_id = 300,
        last_action = {{["content"] = "전투 시작하기", ["event"] = "고민하기"}},
        next = "0",
        pre_id_list = "0",
        status = 2,
        subid = 1,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [400] = {
        content = "꽥꽥꽥,게임을 시작할 수 있습니다.",
        dialog_id = 400,
        last_action = {{["content"] = "전투 시작하기", ["event"] = "고민하기"}},
        next = "0",
        pre_id_list = "0",
        status = 2,
        subid = 1,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [500] = {
        content = "늦었습니다,방금 저분이 저한테서 돈을 다 땄습니다...",
        dialog_id = 500,
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 2,
        subid = 1,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

}
