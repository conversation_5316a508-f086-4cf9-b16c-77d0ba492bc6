-- ./excel/huodong/endless_pve/endless_pve.xlsx
return {

    [20301] = {
        autoteamID = 1002,
        partner_chip_type = 20301,
    },

    [20302] = {
        autoteamID = 1003,
        partner_chip_type = 20302,
    },

    [20303] = {
        autoteamID = 1004,
        partner_chip_type = 20303,
    },

    [20305] = {
        autoteamID = 1005,
        partner_chip_type = 20305,
    },

    [20306] = {
        autoteamID = 1006,
        partner_chip_type = 20306,
    },

    [20308] = {
        autoteamID = 1007,
        partner_chip_type = 20308,
    },

    [20311] = {
        autoteamID = 1008,
        partner_chip_type = 20311,
    },

    [20312] = {
        autoteamID = 1009,
        partner_chip_type = 20312,
    },

    [20313] = {
        autoteamID = 1010,
        partner_chip_type = 20313,
    },

    [20314] = {
        autoteamID = 1011,
        partner_chip_type = 20314,
    },

    [20315] = {
        autoteamID = 1012,
        partner_chip_type = 20315,
    },

    [20316] = {
        autoteamID = 1013,
        partner_chip_type = 20316,
    },

    [20401] = {
        autoteamID = 1014,
        partner_chip_type = 20401,
    },

    [20402] = {
        autoteamID = 1015,
        partner_chip_type = 20402,
    },

    [20403] = {
        autoteamID = 1016,
        partner_chip_type = 20403,
    },

    [20404] = {
        autoteamID = 1017,
        partner_chip_type = 20404,
    },

    [20405] = {
        autoteamID = 1018,
        partner_chip_type = 20405,
    },

    [20407] = {
        autoteamID = 1019,
        partner_chip_type = 20407,
    },

    [20409] = {
        autoteamID = 1020,
        partner_chip_type = 20409,
    },

    [20410] = {
        autoteamID = 1021,
        partner_chip_type = 20410,
    },

    [20412] = {
        autoteamID = 1022,
        partner_chip_type = 20412,
    },

    [20413] = {
        autoteamID = 1023,
        partner_chip_type = 20413,
    },

    [20414] = {
        autoteamID = 1024,
        partner_chip_type = 20414,
    },

    [20415] = {
        autoteamID = 1025,
        partner_chip_type = 20415,
    },

    [20416] = {
        autoteamID = 1026,
        partner_chip_type = 20416,
    },

    [20417] = {
        autoteamID = 1027,
        partner_chip_type = 20417,
    },

    [20418] = {
        autoteamID = 1028,
        partner_chip_type = 20418,
    },

    [20501] = {
        autoteamID = 1029,
        partner_chip_type = 20501,
    },

    [20502] = {
        autoteamID = 1030,
        partner_chip_type = 20502,
    },

    [20503] = {
        autoteamID = 1031,
        partner_chip_type = 20503,
    },

    [20504] = {
        autoteamID = 1032,
        partner_chip_type = 20504,
    },

    [20505] = {
        autoteamID = 1033,
        partner_chip_type = 20505,
    },

    [20506] = {
        autoteamID = 1034,
        partner_chip_type = 20506,
    },

    [20507] = {
        autoteamID = 1035,
        partner_chip_type = 20507,
    },

    [20508] = {
        autoteamID = 1036,
        partner_chip_type = 20508,
    },

    [20509] = {
        autoteamID = 1037,
        partner_chip_type = 20509,
    },

    [20510] = {
        autoteamID = 1038,
        partner_chip_type = 20510,
    },

    [20511] = {
        autoteamID = 1039,
        partner_chip_type = 20511,
    },

    [20512] = {
        autoteamID = 1040,
        partner_chip_type = 20512,
    },

    [20513] = {
        autoteamID = 1041,
        partner_chip_type = 20513,
    },

    [20514] = {
        autoteamID = 1042,
        partner_chip_type = 20514,
    },

}
